# YOLO标注可视化工具

这个工具用于可视化YOLO格式的目标检测数据集标注，特别适用于查看`output-dataset`中的猪检测标注质量。

## 功能特性

- 🎯 **标注可视化**: 在原图上绘制边界框和类别标签
- 📊 **数据集统计**: 生成详细的数据集摘要报告
- 🎲 **随机采样**: 随机选择样本进行可视化检查
- 🎨 **多彩显示**: 为不同类别自动分配不同颜色
- 💾 **结果保存**: 自动保存可视化结果到指定文件夹

## 文件结构

```
annotation_visualizer/
├── visualize_annotations.py  # 主要的可视化类
├── run_visualization.py      # 简单的交互式运行脚本
├── README.md                 # 说明文档
└── visualized_results/       # 输出结果文件夹（自动创建）
    ├── dataset_summary.txt   # 数据集摘要报告
    └── *_annotated.png       # 可视化结果图片
```

## 依赖要求

```bash
pip install opencv-python matplotlib numpy pathlib
```

## 使用方法

### 方法1: 交互式运行（推荐）

```bash
cd annotation_visualizer
python run_visualization.py
```

这将启动一个交互式界面，您可以选择：
1. 随机可视化10个样本
2. 随机可视化指定数量的样本
3. 可视化指定图片
4. 退出

### 方法2: 命令行运行

```bash
# 随机可视化10个样本
python visualize_annotations.py --dataset ../output-dataset --samples 10

# 可视化指定图片
python visualize_annotations.py --dataset ../output-dataset --image output_001

# 自定义输出路径
python visualize_annotations.py --dataset ../output-dataset --output custom_output --samples 5
```

### 方法3: 在Python代码中使用

```python
from visualize_annotations import AnnotationVisualizer

# 创建可视化器
visualizer = AnnotationVisualizer("../output-dataset", "visualized_results")

# 生成数据集报告
visualizer.create_summary_report()

# 随机可视化样本
visualizer.visualize_random_samples(10)

# 可视化指定图片
visualizer.visualize_single_image("output_001")
```

## 输出说明

### 数据集摘要报告 (dataset_summary.txt)
包含以下信息：
- 图片总数
- 标注文件总数
- 标注框总数
- 平均每张图片的标注框数
- 各类别的标注框统计

### 可视化结果图片
- 文件名格式: `{原图名}_annotated.png`
- 在原图上绘制彩色边界框
- 显示类别标签
- 高分辨率保存（150 DPI）

## 数据格式说明

本工具支持标准的YOLO格式：
- 图片文件: `output-dataset/picture/*.jpg`
- 标注文件: `output-dataset/*.txt`
- 类别文件: `output-dataset/classes.txt`

标注文件格式（每行）：
```
class_id x_center y_center width height
```
其中所有坐标都是相对于图片尺寸的归一化值（0-1之间）。

## 注意事项

1. **文件路径**: 确保`output-dataset`文件夹在项目根目录下
2. **图片格式**: 目前支持JPG格式的图片
3. **内存使用**: 处理大量图片时注意内存使用情况
4. **输出文件夹**: 所有输出文件都保存在`annotation_visualizer/visualized_results/`中，不会污染项目主目录

## 故障排除

### 常见问题

1. **找不到数据集**: 检查`output-dataset`文件夹是否存在
2. **图片无法显示**: 确认图片文件格式和路径正确
3. **标注框位置错误**: 检查标注文件格式是否符合YOLO标准
4. **依赖包错误**: 确保安装了所有必需的Python包

### 调试模式

如果遇到问题，可以在代码中添加调试信息：
```python
# 在visualize_single_image方法中添加打印语句
print(f"图片尺寸: {img_width}x{img_height}")
print(f"标注数量: {len(annotations)}")
```

## 扩展功能

可以根据需要扩展以下功能：
- 支持更多图片格式（PNG、BMP等）
- 添加标注质量检查（边界框是否超出图片范围等）
- 支持视频标注可视化
- 添加标注编辑功能
- 导出为其他格式（COCO、Pascal VOC等）
