#!/usr/bin/env python3
"""
批量可视化标注 - 只保存图片，不显示
"""

import cv2
import matplotlib.pyplot as plt
import matplotlib.patches as patches
from pathlib import Path
import random

# 设置matplotlib为非交互模式
plt.ioff()

def load_annotation(annotation_file):
    """加载YOLO格式的标注文件"""
    annotations = []
    if annotation_file.exists():
        with open(annotation_file, 'r') as f:
            for line in f:
                line = line.strip()
                if line:
                    parts = line.split()
                    if len(parts) >= 5:
                        class_id = int(parts[0])
                        x_center = float(parts[1])
                        y_center = float(parts[2])
                        width = float(parts[3])
                        height = float(parts[4])
                        annotations.append((class_id, x_center, y_center, width, height))
    return annotations

def convert_yolo_to_bbox(annotation, img_width, img_height):
    """将YOLO格式坐标转换为边界框坐标"""
    class_id, x_center, y_center, width, height = annotation
    
    x_center *= img_width
    y_center *= img_height
    width *= img_width
    height *= img_height
    
    x1 = int(x_center - width / 2)
    y1 = int(y_center - height / 2)
    x2 = int(x_center + width / 2)
    y2 = int(y_center + height / 2)
    
    return x1, y1, x2, y2

def visualize_image(image_path, annotation_path, output_path):
    """可视化单张图片的标注并保存"""
    try:
        # 读取图片
        image = cv2.imread(str(image_path))
        if image is None:
            print(f"Cannot read image: {image_path}")
            return False
        
        # 转换颜色空间
        image_rgb = cv2.cvtColor(image, cv2.COLOR_BGR2RGB)
        img_height, img_width = image_rgb.shape[:2]
        
        # 加载标注
        annotations = load_annotation(annotation_path)
        
        # 创建图形
        fig, ax = plt.subplots(1, 1, figsize=(12, 8))
        ax.imshow(image_rgb)
        
        # 绘制边界框
        colors = ['red', 'blue', 'green', 'yellow', 'purple', 'orange', 'cyan', 'magenta']
        
        for i, annotation in enumerate(annotations):
            class_id = annotation[0]
            x1, y1, x2, y2 = convert_yolo_to_bbox(annotation, img_width, img_height)
            
            # 选择颜色
            color = colors[class_id % len(colors)]
            
            # 绘制边界框
            rect = patches.Rectangle(
                (x1, y1), x2-x1, y2-y1,
                linewidth=2, edgecolor=color, facecolor='none'
            )
            ax.add_patch(rect)
            
            # 添加标签
            ax.text(x1, y1-5, f"pig_{i+1}", 
                   bbox=dict(boxstyle="round,pad=0.3", facecolor=color, alpha=0.7),
                   fontsize=10, color='white', weight='bold')
        
        # 设置标题
        ax.set_title(f"Annotation: {image_path.name} ({len(annotations)} objects)", 
                    fontsize=14, weight='bold')
        ax.axis('off')
        
        # 保存结果
        plt.savefig(output_path, dpi=150, bbox_inches='tight', pad_inches=0.1)
        plt.close(fig)  # 关闭图形以释放内存
        
        return True
        
    except Exception as e:
        print(f"Error processing {image_path}: {e}")
        return False

def create_summary_report(dataset_path, output_dir):
    """创建数据集摘要报告"""
    image_dir = dataset_path / "picture"
    image_files = list(image_dir.glob("*.jpg"))
    
    total_annotations = 0
    files_with_annotations = 0
    
    for image_file in image_files:
        annotation_file = dataset_path / f"{image_file.stem}.txt"
        annotations = load_annotation(annotation_file)
        if annotations:
            files_with_annotations += 1
            total_annotations += len(annotations)
    
    report = f"""Dataset Annotation Summary
========================

Dataset Path: {dataset_path}
Output Path: {output_dir}

Statistics:
- Total Images: {len(image_files)}
- Images with Annotations: {files_with_annotations}
- Total Annotation Boxes: {total_annotations}
- Average Boxes per Image: {total_annotations/len(image_files):.2f}
- Average Boxes per Annotated Image: {total_annotations/files_with_annotations:.2f}

Class Distribution:
- pig: {total_annotations} boxes
"""
    
    # 保存报告
    report_file = output_dir / "summary_report.txt"
    with open(report_file, 'w', encoding='utf-8') as f:
        f.write(report)
    
    print(report)
    print(f"Report saved to: {report_file}")

def main():
    """主函数"""
    print("=== Batch Annotation Visualizer ===")
    
    # 设置路径
    dataset_path = Path("../output-dataset")
    image_dir = dataset_path / "picture"
    print(f"Dataset Path: {dataset_path}")
    output_dir = Path("visualized_results")
    output_dir.mkdir(exist_ok=True)
    
    if not dataset_path.exists():
        print(f"Dataset not found: {dataset_path}")
        return
    
    # 获取所有图片文件
    image_files = list(image_dir.glob("*.jpg"))
    if not image_files:
        print("No image files found")
        return
    
    print(f"Found {len(image_files)} images")
    
    # 创建摘要报告
    create_summary_report(dataset_path, output_dir)
    
    # 随机选择10个样本进行可视化
    num_samples = min(10, len(image_files))
    selected_files = random.sample(image_files, num_samples)
    
    print(f"\nVisualizing {num_samples} random samples...")
    
    success_count = 0
    for i, image_file in enumerate(selected_files):
        print(f"Processing {i+1}/{num_samples}: {image_file.name}")
        
        # 对应的标注文件
        annotation_file = dataset_path / f"{image_file.stem}.txt"
        
        # 输出文件
        output_file = output_dir / f"{image_file.stem}_annotated.png"
        
        # 可视化
        success = visualize_image(image_file, annotation_file, output_file)
        if success:
            success_count += 1
            print(f"  -> Saved: {output_file.name}")
        else:
            print(f"  -> Failed: {image_file.name}")
    
    print(f"\nCompleted! Successfully processed {success_count}/{num_samples} images.")
    print(f"Check the '{output_dir}' folder for output images.")

if __name__ == "__main__":
    main()
