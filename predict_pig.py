#!/usr/bin/env python3
"""
YOLO11 猪检测推理脚本 - 支持图片、视频、推流三种方式
使用训练好的模型对不同输入源进行猪检测
"""

import os
import argparse
import threading
import subprocess
import time
from ultralytics import YOLO
import cv2
from flask import Flask, send_file, jsonify, render_template_string, Response
import queue
from datetime import datetime

class PigDetectionStreamer:
    def __init__(self, model_path, conf_threshold=0.25, stream_port=8080, hls_port=8081, output_format='hls'):
        """
        初始化检测流媒体器

        Args:
            model_path: YOLO模型路径
            conf_threshold: 置信度阈值
            stream_port: 流媒体服务端口
            hls_port: HLS服务端口
            output_format: 输出格式 ('hls' 或 'flv')
        """
        self.model = YOLO(model_path)
        self.conf_threshold = conf_threshold
        self.stream_port = stream_port
        self.hls_port = hls_port
        self.output_format = output_format
        self.is_streaming = False
        self.frame_queue = queue.Queue(maxsize=10)
        self.detection_stats = {"total_frames": 0, "detected_pigs": 0, "last_detection_time": None}

        # HLS配置
        self.hls_dir = "hls_output"
        self.segment_time = 2  # 每个片段2秒
        self.playlist_size = 5  # 保持5个片段

        # 创建输出目录
        os.makedirs(self.hls_dir, exist_ok=True)
        
        # Flask应用
        self.app = Flask(__name__)
        self.setup_routes()

        # 添加CORS支持
        @self.app.after_request
        def after_request(response):
            response.headers.add('Access-Control-Allow-Origin', '*')
            response.headers.add('Access-Control-Allow-Headers', 'Content-Type,Authorization')
            response.headers.add('Access-Control-Allow-Methods', 'GET,PUT,POST,DELETE,OPTIONS')
            return response
        
    def setup_routes(self):
        """设置Flask路由"""

        # 简化的纯视频播放页面，支持HLS和FLV
        player_html = """
        <!DOCTYPE html>
        <html>
        <head>
            <title>Live Stream</title>
            <meta charset="UTF-8">
            <meta name="viewport" content="width=device-width, initial-scale=1.0">
            <script src="https://cdn.jsdelivr.net/npm/hls.js@latest"></script>
            <script src="https://cdn.jsdelivr.net/npm/flv.js@latest"></script>
            <style>
                body {
                    margin: 0;
                    padding: 0;
                    background: #000;
                    overflow: hidden;
                }
                video {
                    width: 100vw;
                    height: 100vh;
                    object-fit: contain;
                }
                .format-switch {
                    position: absolute;
                    top: 10px;
                    right: 10px;
                    z-index: 1000;
                }
                .format-switch button {
                    background: rgba(0,0,0,0.7);
                    color: white;
                    border: 1px solid #333;
                    padding: 5px 10px;
                    margin: 2px;
                    cursor: pointer;
                }
                .format-switch button.active {
                    background: rgba(255,255,255,0.2);
                }
            </style>
        </head>
        <body>
            <div class="format-switch">
                <button id="hlsBtn" onclick="switchToHLS()">HLS</button>
                <button id="flvBtn" onclick="switchToFLV()">FLV</button>
            </div>
            <video id="video" controls autoplay muted></video>

            <script>
                const video = document.getElementById('video');
                const hlsBtn = document.getElementById('hlsBtn');
                const flvBtn = document.getElementById('flvBtn');
                let currentPlayer = null;

                function switchToHLS() {
                    if (currentPlayer && currentPlayer.destroy) {
                        currentPlayer.destroy();
                    }

                    hlsBtn.classList.add('active');
                    flvBtn.classList.remove('active');

                    if (Hls.isSupported()) {
                        currentPlayer = new Hls({
                            enableWorker: false,
                            lowLatencyMode: true,
                            backBufferLength: 10,
                            maxBufferLength: 30
                        });
                        currentPlayer.loadSource('/live.m3u8');
                        currentPlayer.attachMedia(video);

                        currentPlayer.on(Hls.Events.MANIFEST_PARSED, function() {
                            video.play();
                        });

                        currentPlayer.on(Hls.Events.ERROR, function(event, data) {
                            console.error('HLS错误:', data);
                            if (data.fatal) {
                                setTimeout(() => switchToFLV(), 2000);
                            }
                        });
                    } else if (video.canPlayType('application/vnd.apple.mpegurl')) {
                        video.src = '/live.m3u8';
                        video.play();
                    }
                }

                function switchToFLV() {
                    if (currentPlayer && currentPlayer.destroy) {
                        currentPlayer.destroy();
                    }

                    flvBtn.classList.add('active');
                    hlsBtn.classList.remove('active');

                    if (flvjs.isSupported()) {
                        currentPlayer = flvjs.createPlayer({
                            type: 'flv',
                            url: '/live.flv',
                            isLive: true,
                            hasAudio: false
                        });
                        currentPlayer.attachMediaElement(video);
                        currentPlayer.load();
                        currentPlayer.play();

                        currentPlayer.on(flvjs.Events.ERROR, function(errorType, errorDetail) {
                            console.error('FLV错误:', errorType, errorDetail);
                            setTimeout(() => switchToHLS(), 2000);
                        });
                    }
                }

                // 默认尝试HLS，如果失败则切换到FLV
                switchToHLS();

                // 如果HLS在5秒内没有开始播放，自动切换到FLV
                setTimeout(() => {
                    if (video.readyState === 0) {
                        console.log('HLS加载超时，切换到FLV');
                        switchToFLV();
                    }
                }, 5000);
            </script>
        </body>
        </html>
        """

        @self.app.route('/')
        def index():
            """主页 - 直接播放视频流"""
            return render_template_string(player_html, port=self.hls_port)

        @self.app.route('/live.m3u8')
        def serve_m3u8():
            """提供m3u8播放列表"""
            m3u8_path = os.path.join(self.hls_dir, "live.m3u8")
            if os.path.exists(m3u8_path):
                response = send_file(m3u8_path, mimetype='application/vnd.apple.mpegurl')
                response.headers['Cache-Control'] = 'no-cache, no-store, must-revalidate'
                response.headers['Pragma'] = 'no-cache'
                response.headers['Expires'] = '0'
                return response
            return "Stream not available", 404

        @self.app.route('/live<int:segment_num>.ts')
        def serve_segment(segment_num):
            """提供TS片段"""
            segment_path = os.path.join(self.hls_dir, f"live{segment_num:03d}.ts")
            if os.path.exists(segment_path):
                response = send_file(segment_path, mimetype='video/mp2t')
                response.headers['Cache-Control'] = 'max-age=3600'
                return response
            return "Segment not found", 404

        @self.app.route('/stats')
        def get_stats():
            """获取检测统计信息"""
            return jsonify(self.detection_stats)

        @self.app.route('/live.flv')
        def serve_flv():
            """提供FLV流"""
            flv_path = os.path.join(self.hls_dir, "live.flv")
            if os.path.exists(flv_path):
                def generate():
                    with open(flv_path, 'rb') as f:
                        while True:
                            data = f.read(1024)
                            if not data:
                                time.sleep(0.1)
                                continue
                            yield data
                return Response(generate(), mimetype='video/x-flv')
            return "FLV stream not available", 404

        @self.app.route('/stream_url')
        def get_stream_url():
            """获取流媒体URL"""
            if self.output_format == 'flv':
                return jsonify({
                    "flv_url": f"http://localhost:{self.hls_port}/live.flv",
                    "hls_url": f"http://localhost:{self.hls_port}/live.m3u8",
                    "status": "streaming" if self.is_streaming else "stopped",
                    "format": "flv"
                })
            else:
                return jsonify({
                    "hls_url": f"http://localhost:{self.hls_port}/live.m3u8",
                    "flv_url": f"http://localhost:{self.hls_port}/live.flv",
                    "status": "streaming" if self.is_streaming else "stopped",
                    "format": "hls"
                })

def calculate_optimal_resolution(original_width, original_height, target_size=640):
    """
    计算最优分辨率，保持宽高比

    Args:
        original_width: 原始宽度
        original_height: 原始高度
        target_size: 目标尺寸（较长边）

    Returns:
        tuple: (新宽度, 新高度, 缩放比例)
    """
    # 计算宽高比
    aspect_ratio = original_width / original_height

    # 确定较长边
    if original_width > original_height:
        new_width = target_size
        new_height = int(target_size / aspect_ratio)
    else:
        new_height = target_size
        new_width = int(target_size * aspect_ratio)

    # 确保尺寸是偶数（FFmpeg要求）
    new_width = new_width if new_width % 2 == 0 else new_width - 1
    new_height = new_height if new_height % 2 == 0 else new_height - 1

    scale_factor = min(new_width / original_width, new_height / original_height)

    return new_width, new_height, scale_factor

def draw_boxes_only(image, boxes, thickness=1):
    """
    只绘制边界框，不绘制标签和置信度

    Args:
        image: 原始图像
        boxes: 检测框
        thickness: 线条粗细
    """
    if boxes is None:
        return image

    # 复制图像以避免修改原图
    annotated_img = image.copy()

    # 绘制每个检测框
    for box in boxes:
        x1, y1, x2, y2 = box.xyxy[0].cpu().numpy().astype(int)
        # 只绘制边界框，使用较细的线条
        cv2.rectangle(annotated_img, (x1, y1), (x2, y2), (255, 255, 0), thickness=3)

    return annotated_img

def predict_image(model, image_path, conf_threshold=0.25, iou_threshold=0.45,
                 max_det=300, save_dir="predictions", imgsz=640):
    """
    对单张图片进行预测

    Args:
        model: YOLO模型
        image_path: 图片路径
        conf_threshold: 置信度阈值
        iou_threshold: IOU阈值
        max_det: 最大检测数量
        save_dir: 保存目录
        imgsz: 推理图像尺寸
    """
    if not os.path.exists(image_path):
        print(f"错误: 图片文件 {image_path} 不存在!")
        return
    
    print(f"正在预测图片: {image_path}")
    
    # 进行预测
    results = model(image_path, conf=conf_threshold, iou=iou_threshold, max_det=max_det, imgsz=imgsz, verbose=False)
    
    # 保存结果
    os.makedirs(save_dir, exist_ok=True)
    
    for i, result in enumerate(results):
        # 读取原始图像
        original_img = cv2.imread(image_path)
        
        # 只绘制边界框，线条粗细设为1
        annotated_img = draw_boxes_only(original_img, result.boxes, thickness=1)
        
        output_path = os.path.join(save_dir, f"predicted_{os.path.basename(image_path)}")
        cv2.imwrite(output_path, annotated_img)
        print(f"预测结果已保存到: {output_path}")
        
        # 打印检测结果
        if result.boxes is not None:
            boxes = result.boxes
            print(f"检测到 {len(boxes)} 只猪:")
            for j, box in enumerate(boxes):
                conf = box.conf[0].item()
                x1, y1, x2, y2 = box.xyxy[0].tolist()
                print(f"  猪 {j+1}: 置信度={conf:.3f}, 坐标=({x1:.1f}, {y1:.1f}, {x2:.1f}, {y2:.1f})")
        else:
            print("未检测到猪")

def predict_video(model, video_path, conf_threshold=0.25, iou_threshold=0.45,
                 max_det=300, save_dir="predictions", imgsz=640):
    """
    对视频进行预测

    Args:
        model: YOLO模型
        video_path: 视频路径
        conf_threshold: 置信度阈值
        iou_threshold: IOU阈值
        max_det: 最大检测数量
        save_dir: 保存目录
        imgsz: 推理图像尺寸
    """
    if not os.path.exists(video_path):
        print(f"错误: 视频文件 {video_path} 不存在!")
        return
    
    print(f"正在预测视频: {video_path}")
    
    # 打开视频
    cap = cv2.VideoCapture(video_path)
    if not cap.isOpened():
        print(f"错误: 无法打开视频文件 {video_path}")
        return
    
    # 获取视频信息
    fps = int(cap.get(cv2.CAP_PROP_FPS))
    width = int(cap.get(cv2.CAP_PROP_FRAME_WIDTH))
    height = int(cap.get(cv2.CAP_PROP_FRAME_HEIGHT))
    total_frames = int(cap.get(cv2.CAP_PROP_FRAME_COUNT))
    
    print(f"视频信息: {width}x{height}, {fps}fps, {total_frames}帧")
    
    # 创建输出视频
    os.makedirs(save_dir, exist_ok=True)
    output_path = os.path.join(save_dir, f"predicted_{os.path.basename(video_path)}")
    fourcc = cv2.VideoWriter_fourcc(*'mp4v')
    out = cv2.VideoWriter(output_path, fourcc, fps, (width, height))
    
    frame_count = 0
    total_detections = 0
    
    while True:
        ret, frame = cap.read()
        if not ret:
            break
        
        frame_count += 1
        print(f"处理帧 {frame_count}/{total_frames}", end='\r')
        
        # 进行预测
        results = model(frame, conf=conf_threshold, iou=iou_threshold, max_det=max_det, imgsz=imgsz, verbose=False)
        
        # 统计检测结果
        if results[0].boxes is not None:
            total_detections += len(results[0].boxes)
        
        # 只绘制边界框，线条粗细设为1
        annotated_frame = draw_boxes_only(frame, results[0].boxes, thickness=1)
        
        # 写入输出视频
        out.write(annotated_frame)
    
    # 释放资源
    cap.release()
    out.release()
    print(f"\n预测完成! 结果已保存到: {output_path}")
    print(f"总检测数量: {total_detections}")

def test_source_connection(source):
    """
    测试输入源连接

    Args:
        source: 输入源地址

    Returns:
        bool: 连接是否成功
    """
    print(f"🔍 测试输入源连接: {source}")

    # 检测输入源类型
    if source.startswith('wss://') or 'wss%3A%2F%2F' in source:
        print("❌ WebSocket流不被支持")
        return False

    try:
        if source.isdigit():
            print(f"📷 测试摄像头 {source}...")
            cap = cv2.VideoCapture(int(source))
        else:
            print(f"🌐 测试流地址...")
            cap = cv2.VideoCapture(source)

        if cap.isOpened():
            # 尝试读取一帧
            ret, frame = cap.read()
            if ret:
                height, width = frame.shape[:2]
                fps = cap.get(cv2.CAP_PROP_FPS)
                print(f"✅ 连接成功!")
                print(f"   分辨率: {width}x{height}")
                print(f"   帧率: {fps:.1f} fps")
                cap.release()
                return True
            else:
                print("❌ 无法读取视频帧")
                cap.release()
                return False
        else:
            print("❌ 无法打开输入源")
            return False

    except Exception as e:
        print(f"❌ 连接测试失败: {e}")
        return False

def start_streaming(model, source, conf_threshold=0.25, iou_threshold=0.45,
                   max_det=300, stream_port=8080, hls_port=8081, imgsz=640, output_format='hls'):
    """
    启动推流服务

    Args:
        model: YOLO模型
        source: 输入源 (摄像头ID/视频文件/RTMP流)
        conf_threshold: 置信度阈值
        iou_threshold: IOU阈值
        max_det: 最大检测数量
        stream_port: 流媒体服务端口
        hls_port: HLS服务端口
        imgsz: 目标图像尺寸
        output_format: 输出格式 ('hls' 或 'flv')
    """
    streamer = PigDetectionStreamer(model, conf_threshold, stream_port, hls_port, output_format)
    
    # 启动HLS服务器
    def run_hls_server():
        streamer.app.run(host='0.0.0.0', port=hls_port, debug=False, threaded=True)
    
    hls_thread = threading.Thread(target=run_hls_server)
    hls_thread.daemon = True
    hls_thread.start()
    
    print(f"HLS服务器已启动: http://localhost:{hls_port}")
    print(f"直播流地址: http://localhost:{hls_port}/live.m3u8")
    print(f"统计信息: http://localhost:{hls_port}/stats")
    
    # 打开输入源
    print(f"正在连接输入源: {source}")

    # 检测输入源类型并进行相应处理
    cap = None

    # 处理不同类型的输入源
    if source.startswith('wss://') or 'wss%3A%2F%2F' in source:
        print("❌ 检测到WebSocket流地址")
        print("OpenCV不支持WebSocket流，请使用以下格式之一：")
        print("  📹 RTMP流：rtmp://server/live/stream")
        print("  🌐 HTTP流：http://server/stream.m3u8")
        print("  📷 本地摄像头：0, 1, 2...")
        print("  📁 本地视频：path/to/video.mp4")
        return

    elif source.startswith('rtmp://'):
        print("📹 检测到RTMP流地址")
        cap = cv2.VideoCapture(source)
        # 设置RTMP流的缓冲区参数
        cap.set(cv2.CAP_PROP_BUFFERSIZE, 1)  # 减少缓冲区大小
        cap.set(cv2.CAP_PROP_FPS, 30)  # 设置期望帧率

    elif source.startswith('http://') or source.startswith('https://'):
        print("🌐 检测到HTTP流地址")
        cap = cv2.VideoCapture(source)
        # 设置HTTP流的缓冲区参数
        cap.set(cv2.CAP_PROP_BUFFERSIZE, 1)

    elif source.isdigit():
        print(f"📷 检测到摄像头ID: {source}")
        cap = cv2.VideoCapture(int(source))

    elif os.path.isfile(source):
        print(f"📁 检测到本地视频文件: {source}")
        cap = cv2.VideoCapture(source)

    else:
        print(f"🔍 尝试作为通用流地址处理: {source}")
        try:
            # 尝试将source转换为整数（摄像头ID）
            cap = cv2.VideoCapture(int(source))
        except ValueError:
            # 如果不是整数，则作为文件路径或流地址
            cap = cv2.VideoCapture(source)

    if cap is None or not cap.isOpened():
        print(f"❌ 错误: 无法打开输入源 {source}")
        print("\n🔧 可能的解决方案:")

        if source.isdigit():
            print(f"📷 摄像头 {source} 可能不存在或被占用")
            print("   - 尝试其他摄像头ID: 0, 1, 2...")
            print("   - 检查摄像头是否被其他程序占用")
            print("   - 确认摄像头驱动是否正常")

        elif source.startswith('rtmp://'):
            print("📹 RTMP流连接失败")
            print("   - 检查RTMP服务器是否运行")
            print("   - 验证流地址是否正确")
            print("   - 确认网络连接正常")

        elif source.startswith('http'):
            print("🌐 HTTP流连接失败")
            print("   - 检查HTTP服务器是否可达")
            print("   - 验证流地址是否正确")
            print("   - 尝试在浏览器中打开该地址")

        else:
            print("📁 文件或流地址无效")
            print("   - 检查文件路径是否正确")
            print("   - 确认文件格式是否支持")
            print("   - 验证网络连接状态")

        print("\n💡 建议的测试方法:")
        print("   python predict_pig.py --mode stream --source 0  # 测试摄像头")
        print("   python predict_pig.py --mode stream --source rtmp://your-server/live/stream")
        print("   python predict_pig.py --mode stream --source http://your-server/stream.m3u8")
        return

    # 获取视频信息
    fps = int(cap.get(cv2.CAP_PROP_FPS)) or 30
    original_width = int(cap.get(cv2.CAP_PROP_FRAME_WIDTH))
    original_height = int(cap.get(cv2.CAP_PROP_FRAME_HEIGHT))

    print(f"原始视频信息: {original_width}x{original_height}, {fps}fps")

    # 检查分辨率并计算缩放
    scale_factor = 1.0
    output_width, output_height = original_width, original_height

    # 如果分辨率超过2K (2048x1080) 或任一边超过imgsz*2，则进行缩放
    if original_width > 2048 or original_height > 1080 or original_width > imgsz*2 or original_height > imgsz*2:
        output_width, output_height, scale_factor = calculate_optimal_resolution(
            original_width, original_height, imgsz
        )
        print(f"检测到高分辨率视频，将缩放到: {output_width}x{output_height}")
        print(f"缩放比例: {scale_factor:.3f}")
    else:
        print("分辨率适中，不需要缩放")

    width, height = output_width, output_height
    
    # 生成FFmpeg命令 - 同时输出HLS和FLV
    ffmpeg_cmd = [
        'ffmpeg',
        '-y',  # 覆盖输出文件
        '-f', 'rawvideo',
        '-vcodec', 'rawvideo',
        '-pix_fmt', 'bgr24',
        '-s', f'{width}x{height}',
        '-r', str(fps),
        '-i', '-',  # 从stdin读取

        # 视频编码设置
        '-c:v', 'libx264',
        '-preset', 'ultrafast',
        '-tune', 'zerolatency',
        '-crf', '23',
        '-maxrate', '2000k',
        '-bufsize', '1000k',
        '-g', str(fps),
        '-sc_threshold', '0',

        # HLS输出
        '-f', 'hls',
        '-hls_time', str(streamer.segment_time),
        '-hls_list_size', str(streamer.playlist_size),
        '-hls_flags', 'delete_segments+round_durations+discont_start',
        '-hls_segment_type', 'mpegts',
        '-hls_segment_filename', os.path.join(streamer.hls_dir, 'live%03d.ts'),
        '-hls_allow_cache', '0',
        '-hls_wrap', str(streamer.playlist_size),
        os.path.join(streamer.hls_dir, 'live.m3u8'),

        # FLV输出
        '-f', 'flv',
        '-flvflags', 'no_duration_filesize',
        os.path.join(streamer.hls_dir, 'live.flv')
    ]
    
    def restart_ffmpeg():
        """重启FFmpeg进程"""
        nonlocal ffmpeg_process
        try:
            if ffmpeg_process:
                ffmpeg_process.stdin.close()
                ffmpeg_process.terminate()
                ffmpeg_process.wait(timeout=5)
        except:
            pass

        print("🔄 重启FFmpeg进程...")
        ffmpeg_process = subprocess.Popen(ffmpeg_cmd, stdin=subprocess.PIPE,
                                        stderr=subprocess.PIPE, bufsize=0)
        return ffmpeg_process

    try:
        ffmpeg_process = subprocess.Popen(ffmpeg_cmd, stdin=subprocess.PIPE,
                                        stderr=subprocess.PIPE, bufsize=0)

        streamer.is_streaming = True
        frame_count = 0
        ffmpeg_restart_count = 0
        max_ffmpeg_restarts = 3

        print("开始推流...")
        
        # 添加重连机制
        reconnect_attempts = 0
        max_reconnect_attempts = 5
        last_frame_time = time.time()
        frame_timeout = 10  # 10秒无帧则重连

        while True:
            ret, frame = cap.read()
            current_time = time.time()

            if not ret:
                print(f"⚠️ 无法读取帧，尝试重连... (尝试 {reconnect_attempts + 1}/{max_reconnect_attempts})")

                # 释放当前连接
                cap.release()
                time.sleep(2)  # 等待2秒再重连

                # 重新连接
                if source.isdigit():
                    cap = cv2.VideoCapture(int(source))
                else:
                    cap = cv2.VideoCapture(source)

                reconnect_attempts += 1

                if cap.isOpened():
                    print("✅ 重连成功")
                    reconnect_attempts = 0  # 重置重连计数
                    last_frame_time = current_time
                    continue
                elif reconnect_attempts >= max_reconnect_attempts:
                    print(f"❌ 重连失败，已尝试 {max_reconnect_attempts} 次，退出...")
                    break
                else:
                    continue

            # 检查帧超时
            if current_time - last_frame_time > frame_timeout:
                print(f"⚠️ 帧超时 ({frame_timeout}秒无新帧)，尝试重连...")
                cap.release()
                time.sleep(1)

                if source.isdigit():
                    cap = cv2.VideoCapture(int(source))
                else:
                    cap = cv2.VideoCapture(source)

                if cap.isOpened():
                    print("✅ 超时重连成功")
                    last_frame_time = current_time
                    continue
                else:
                    print("❌ 超时重连失败")
                    break

            # 更新最后帧时间
            last_frame_time = current_time
            reconnect_attempts = 0  # 成功读取帧，重置重连计数

            frame_count += 1
            streamer.detection_stats["total_frames"] = frame_count

            # 如果需要缩放，先缩放帧
            if scale_factor != 1.0:
                frame = cv2.resize(frame, (output_width, output_height), interpolation=cv2.INTER_LINEAR)

            # 进行预测（指定imgsz，关闭详细输出）
            results = model(frame, conf=conf_threshold, iou=iou_threshold, max_det=max_det, imgsz=imgsz, verbose=False)

            # 统计检测结果
            if results[0].boxes is not None:
                pig_count = len(results[0].boxes)
                streamer.detection_stats["detected_pigs"] += pig_count
                if pig_count > 0:
                    streamer.detection_stats["last_detection_time"] = datetime.now().isoformat()

            # 只绘制边界框
            annotated_frame = draw_boxes_only(frame, results[0].boxes, thickness=1)

            # 发送到FFmpeg
            try:
                if ffmpeg_process.poll() is None:  # 检查进程是否还在运行
                    ffmpeg_process.stdin.write(annotated_frame.tobytes())
                    ffmpeg_process.stdin.flush()
                else:
                    print("⚠️ FFmpeg进程已停止，尝试重启...")
                    ffmpeg_process = restart_ffmpeg()
                    if ffmpeg_process:
                        ffmpeg_process.stdin.write(annotated_frame.tobytes())
                        ffmpeg_process.stdin.flush()
                    else:
                        print("❌ FFmpeg重启失败")
                        break
            except (BrokenPipeError, OSError) as e:
                print(f"⚠️ FFmpeg写入错误: {e}，尝试重启...")
                ffmpeg_process = restart_ffmpeg()
                if not ffmpeg_process:
                    print("❌ FFmpeg重启失败，停止推流")
                    break

            # 显示实时统计（每100帧）
            if frame_count % 100 == 0:
                print(f"已处理 {frame_count} 帧，检测到 {streamer.detection_stats['detected_pigs']} 只猪")
                if scale_factor != 1.0:
                    print(f"当前输出分辨率: {output_width}x{output_height} (缩放比例: {scale_factor:.3f})")
    
    except KeyboardInterrupt:
        print("\n正在停止推流...")
    
    finally:
        streamer.is_streaming = False
        cap.release()
        if ffmpeg_process:
            ffmpeg_process.stdin.close()
            ffmpeg_process.terminate()
            ffmpeg_process.wait()
        print("推流已停止")

def predict_folder(model, folder_path, conf_threshold=0.25, iou_threshold=0.45,
                  max_det=300, save_dir="predictions", imgsz=640):
    """
    对文件夹中的所有图片进行预测

    Args:
        model: YOLO模型
        folder_path: 文件夹路径
        conf_threshold: 置信度阈值
        iou_threshold: IOU阈值
        max_det: 最大检测数量
        save_dir: 保存目录
        imgsz: 推理图像尺寸
    """
    if not os.path.exists(folder_path):
        print(f"错误: 文件夹 {folder_path} 不存在!")
        return
    
    # 支持的图片格式
    image_extensions = {'.jpg', '.jpeg', '.png', '.bmp', '.tiff', '.tif'}
    
    # 获取所有图片文件
    image_files = []
    for file in os.listdir(folder_path):
        if os.path.splitext(file)[1].lower() in image_extensions:
            image_files.append(os.path.join(folder_path, file))
    
    if not image_files:
        print(f"在文件夹 {folder_path} 中未找到图片文件")
        return
    
    print(f"找到 {len(image_files)} 张图片")
    
    # 批量预测
    for i, image_path in enumerate(image_files):
        print(f"处理图片 {i+1}/{len(image_files)}: {os.path.basename(image_path)}")
        predict_image(model, image_path, conf_threshold, iou_threshold, max_det, save_dir, imgsz)

def main():
    parser = argparse.ArgumentParser(description='YOLO11 猪检测推理脚本 - 支持图片、视频、推流')
    parser.add_argument('--model', type=str, default='runs/detect/pig_detection/weights/best.pt',
                       help='模型路径')
    parser.add_argument('--source', type=str, required=False,
                       help='输入源 (图片路径/视频路径/文件夹路径/摄像头ID/RTMP流)')
    parser.add_argument('--mode', type=str, choices=['image', 'video', 'stream'],
                       default='auto', help='运行模式: image(图片), video(视频), stream(推流)')
    
    # 推理参数
    parser.add_argument('--conf', type=float, default=0.25,
                       help='置信度阈值 (0.0-1.0)')
    parser.add_argument('--iou', type=float, default=0.45,
                       help='NMS IOU阈值 (0.0-1.0)')
    parser.add_argument('--max-det', type=int, default=300,
                       help='每张图片的最大检测数量')
    parser.add_argument('--imgsz', type=int, default=640,
                       help='推理图片大小')
    parser.add_argument('--device', type=str, default='',
                       help='设备 (cpu, 0, 1, 2, 3, auto)')
    parser.add_argument('--half', action='store_true',
                       help='使用FP16半精度推理')
    parser.add_argument('--augment', action='store_true',
                       help='增强推理')
    parser.add_argument('--agnostic-nms', action='store_true',
                       help='类别无关的NMS')
    
    # 输出参数
    parser.add_argument('--save-dir', type=str, default='predictions',
                       help='保存目录')
    parser.add_argument('--stream-port', type=int, default=8080,
                       help='流媒体服务端口')
    parser.add_argument('--hls-port', type=int, default=8081,
                       help='HLS服务端口')
    parser.add_argument('--test-source', action='store_true',
                       help='测试输入源连接（不进行检测）')
    parser.add_argument('--output-format', type=str, choices=['hls', 'flv'], default='hls',
                       help='输出流格式: hls(默认) 或 flv')
    
    args = parser.parse_args()

    # 处理source参数的默认值
    if args.source is None:
        if args.mode == 'stream':
            # stream模式下使用默认的视频流地址
            # 注意：WebSocket流需要特殊处理，这里提供一个测试用的RTMP流
            print("注意：原始WebSocket流地址无法直接使用，建议使用以下格式：")
            print("1. RTMP流：rtmp://server/live/stream")
            print("2. HTTP流：http://server/stream.m3u8")
            print("3. 本地摄像头：0, 1, 2...")
            print("4. 本地视频文件：path/to/video.mp4")
            print()
            print("当前将尝试使用摄像头0作为默认输入源")
            args.source = '0'  # 使用默认摄像头
            print(f"使用默认输入源: 摄像头 {args.source}")
        else:
            print("错误: 必须指定输入源 (--source)")
            print("或者使用 --mode stream 来使用默认输入源")
            return

    # 如果是测试源连接模式
    if args.test_source:
        print("🔧 测试源连接模式")
        success = test_source_connection(args.source)
        if success:
            print("✅ 输入源连接测试成功！可以开始正常检测。")
        else:
            print("❌ 输入源连接测试失败！请检查源地址。")
        return

    # 检查模型文件是否存在
    if not os.path.exists(args.model):
        print(f"错误: 模型文件 {args.model} 不存在!")
        print("请先训练模型或提供正确的模型路径")
        return

    # 加载模型
    print(f"加载模型: {args.model}")
    model = YOLO(args.model)
    
    # 设置模型参数
    if args.device:
        model.to(args.device)
    if args.half:
        model.half()
    
    # 自动判断模式
    if args.mode == 'auto':
        if os.path.isfile(args.source):
            file_ext = os.path.splitext(args.source)[1].lower()
            if file_ext in {'.jpg', '.jpeg', '.png', '.bmp', '.tiff', '.tif'}:
                args.mode = 'image'
            elif file_ext in {'.mp4', '.avi', '.mov', '.mkv', '.flv', '.wmv'}:
                args.mode = 'video'
            else:
                print(f"不支持的文件格式: {file_ext}")
                return
        elif os.path.isdir(args.source):
            args.mode = 'image'  # 文件夹模式
        else:
            # 可能是摄像头ID或流地址
            args.mode = 'stream'
    
    # 打印配置信息
    print(f"运行模式: {args.mode}")
    print(f"推理参数: conf={args.conf}, iou={args.iou}, max_det={args.max_det}")
    print(f"图片大小: {args.imgsz}, 设备: {args.device or 'auto'}")
    print(f"半精度: {args.half}, 增强推理: {args.augment}")
    
    # 根据模式执行不同的预测
    if args.mode == 'image':
        if os.path.isfile(args.source):
            predict_image(model, args.source, args.conf, args.iou, args.max_det, args.save_dir, args.imgsz)
        elif os.path.isdir(args.source):
            predict_folder(model, args.source, args.conf, args.iou, args.max_det, args.save_dir, args.imgsz)
        else:
            print(f"错误: 输入源 {args.source} 不存在!")

    elif args.mode == 'video':
        predict_video(model, args.source, args.conf, args.iou, args.max_det, args.save_dir, args.imgsz)
    
    elif args.mode == 'stream':
        start_streaming(model, args.source, args.conf, args.iou, args.max_det,
                       args.stream_port, args.hls_port, args.imgsz, args.output_format)
    
    else:
        print(f"不支持的模式: {args.mode}")

if __name__ == "__main__":
    main()
