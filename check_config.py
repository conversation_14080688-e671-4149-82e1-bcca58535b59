#!/usr/bin/env python3
"""
配置检查脚本
检查系统环境和依赖是否满足运行要求
"""

import os
import sys
import subprocess
import importlib
from pathlib import Path

def print_header(title):
    """打印标题"""
    print("\n" + "="*60)
    print(f" {title}")
    print("="*60)

def print_status(item, status, details=""):
    """打印状态"""
    icon = "✓" if status else "✗"
    status_text = "OK" if status else "FAIL"
    print(f"{icon} {item:<30} [{status_text}] {details}")

def check_python():
    """检查Python环境"""
    print_header("Python环境检查")
    
    # Python版本
    version = sys.version_info
    version_str = f"{version.major}.{version.minor}.{version.micro}"
    python_ok = version.major >= 3 and version.minor >= 7
    print_status("Python版本", python_ok, f"v{version_str} (需要 >= 3.7)")
    
    # pip
    try:
        import pip
        pip_version = pip.__version__
        print_status("pip", True, f"v{pip_version}")
    except ImportError:
        print_status("pip", False, "未安装")
    
    return python_ok

def check_system_tools():
    """检查系统工具"""
    print_header("系统工具检查")
    
    tools = {
        "ffmpeg": ["ffmpeg", "-version"],
        "git": ["git", "--version"]
    }
    
    results = {}
    for tool, cmd in tools.items():
        try:
            result = subprocess.run(cmd, capture_output=True, text=True, timeout=10)
            if result.returncode == 0:
                # 提取版本信息
                version_line = result.stdout.split('\n')[0]
                print_status(tool, True, version_line[:50] + "..." if len(version_line) > 50 else version_line)
                results[tool] = True
            else:
                print_status(tool, False, "命令执行失败")
                results[tool] = False
        except (subprocess.TimeoutExpired, FileNotFoundError, subprocess.SubprocessError):
            print_status(tool, False, "未安装或不在PATH中")
            results[tool] = False
    
    return results

def check_python_packages():
    """检查Python包"""
    print_header("Python包检查")
    
    required_packages = {
        "ultralytics": "YOLO模型库",
        "cv2": "OpenCV图像处理",
        "flask": "Web服务框架",
        "numpy": "数值计算",
        "torch": "PyTorch深度学习框架",
        "torchvision": "PyTorch视觉库"
    }
    
    results = {}
    for package, description in required_packages.items():
        try:
            if package == "cv2":
                # OpenCV的包名是cv2，但安装名是opencv-python
                import cv2
                version = cv2.__version__
            else:
                module = importlib.import_module(package)
                version = getattr(module, '__version__', 'unknown')
            
            print_status(f"{package} ({description})", True, f"v{version}")
            results[package] = True
        except ImportError:
            print_status(f"{package} ({description})", False, "未安装")
            results[package] = False
    
    return results

def check_model_files():
    """检查模型文件"""
    print_header("模型文件检查")
    
    model_paths = [
        "runs/detect/pig_detection/weights/best.pt",
        "yolo11x.pt",
        "yolo11n.pt",
        "yolo11s.pt",
        "yolo11m.pt",
        "yolo11l.pt"
    ]
    
    found_models = []
    for model_path in model_paths:
        if os.path.exists(model_path):
            size = os.path.getsize(model_path)
            size_mb = size / (1024 * 1024)
            print_status(f"模型文件", True, f"{model_path} ({size_mb:.1f} MB)")
            found_models.append(model_path)
        else:
            print_status(f"模型文件", False, f"{model_path} (不存在)")
    
    if not found_models:
        print("\n💡 提示: 如果没有训练好的模型，系统会自动下载YOLO11x.pt预训练模型")
    
    return len(found_models) > 0

def check_project_files():
    """检查项目文件"""
    print_header("项目文件检查")
    
    required_files = {
        "predict_pig.py": "主推理脚本",
        "start_pig_stream.py": "启动脚本",
        "test_stream_connection.py": "连接测试脚本",
        "monitor_service.py": "监控脚本"
    }
    
    results = {}
    for filename, description in required_files.items():
        exists = os.path.exists(filename)
        if exists:
            size = os.path.getsize(filename)
            print_status(f"{filename} ({description})", True, f"{size} bytes")
        else:
            print_status(f"{filename} ({description})", False, "文件不存在")
        results[filename] = exists
    
    return results

def check_network():
    """检查网络连接"""
    print_header("网络连接检查")
    
    # 测试基本网络连接
    test_hosts = [
        ("cameracc.cdelinks.cn", "视频流服务器"),
        ("github.com", "GitHub (模型下载)"),
        ("pypi.org", "PyPI (包下载)")
    ]
    
    results = {}
    for host, description in test_hosts:
        try:
            # 使用ping命令测试连接
            if os.name == 'nt':  # Windows
                cmd = ["ping", "-n", "1", host]
            else:  # Linux/Mac
                cmd = ["ping", "-c", "1", host]
            
            result = subprocess.run(cmd, capture_output=True, text=True, timeout=5)
            if result.returncode == 0:
                print_status(f"{host} ({description})", True, "连接正常")
                results[host] = True
            else:
                print_status(f"{host} ({description})", False, "连接失败")
                results[host] = False
        except (subprocess.TimeoutExpired, FileNotFoundError):
            print_status(f"{host} ({description})", False, "无法测试连接")
            results[host] = False
    
    return results

def generate_report(checks):
    """生成检查报告"""
    print_header("检查报告")
    
    total_checks = 0
    passed_checks = 0
    
    for category, results in checks.items():
        if isinstance(results, dict):
            for item, status in results.items():
                total_checks += 1
                if status:
                    passed_checks += 1
        elif isinstance(results, bool):
            total_checks += 1
            if results:
                passed_checks += 1
    
    success_rate = (passed_checks / total_checks) * 100 if total_checks > 0 else 0
    
    print(f"总检查项: {total_checks}")
    print(f"通过检查: {passed_checks}")
    print(f"成功率: {success_rate:.1f}%")
    
    if success_rate >= 80:
        print("\n🎉 系统配置良好，可以启动服务！")
        print("运行命令: python start_pig_stream.py --port 8081")
    elif success_rate >= 60:
        print("\n⚠️  系统配置基本满足要求，但建议解决失败的检查项")
        print("可以尝试启动服务，但可能遇到问题")
    else:
        print("\n❌ 系统配置不满足要求，请解决失败的检查项后重试")
        print("建议运行: pip install ultralytics opencv-python flask numpy torch torchvision")

def main():
    """主函数"""
    print("🔍 猪检测推流服务配置检查")
    
    checks = {}
    
    # 执行各项检查
    checks['python'] = check_python()
    checks['system_tools'] = check_system_tools()
    checks['python_packages'] = check_python_packages()
    checks['model_files'] = check_model_files()
    checks['project_files'] = check_project_files()
    checks['network'] = check_network()
    
    # 生成报告
    generate_report(checks)

if __name__ == "__main__":
    main()
