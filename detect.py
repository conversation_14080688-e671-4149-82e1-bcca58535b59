#!/usr/bin/env python3
"""
YOLO11 目标检测推理脚本
基于Ultralytics官方命令格式，支持图片和视频识别
支持TensorRT加速和多GPU并行推理
"""

from ultralytics import YOLO
from pathlib import Path
import argparse
import sys
import threading
import time
from concurrent.futures import ThreadPoolExecutor, as_completed
import torch

def find_model():
    """查找可用模型"""
    model_paths = [
        "runs/train/*/weights/best.pt",
        "runs/train/*/weights/last.pt",
        "yolo11n.pt",
        "yolo11s.pt",
        "yolo11m.pt",
        "yolo11l.pt",
        "yolo11x.pt"
    ]

    for pattern in model_paths:
        if '*' in pattern:
            # 使用glob查找匹配的路径
            matches = list(Path('.').glob(pattern))
            if matches:
                return str(matches[0])
        else:
            if Path(pattern).exists():
                return pattern
    return None

def export_tensorrt(model_path, **kwargs):
    """
    导出TensorRT模型

    Args:
        model_path: 原始模型路径
        **kwargs: 导出参数
    """
    print(f"🔧 导出TensorRT模型...")
    print(f"📦 原始模型: {model_path}")

    try:
        model = YOLO(model_path)

        # TensorRT导出参数
        export_args = {
            'format': 'engine',
            'imgsz': kwargs.get('imgsz', 640),
            'half': kwargs.get('half', True),  # FP16精度
            'int8': kwargs.get('int8', False),  # INT8量化
            'dynamic': kwargs.get('dynamic', False),  # 动态batch
            'workspace': kwargs.get('workspace', 16),  # 工作空间大小(GB)
            'device': kwargs.get('device', 0),
            'simplify':kwargs.get('simplify', True),
            'verbose': False
        }

        if export_args['int8']:
            export_args['data'] = kwargs.get('data', 'coco.yaml')

        print("🔧 TensorRT导出配置:")
        for key, value in export_args.items():
            print(f"  {key}: {value}")

        # 执行导出
        model.export(**export_args)

        engine_path = str(Path(model_path).with_suffix('.engine'))
        print(f"✅ TensorRT模型导出成功: {engine_path}")
        return engine_path

    except Exception as e:
        print(f"❌ TensorRT导出失败: {e}")
        return None

def detect_single_gpu(model_path, source, **kwargs):
    """
    单GPU检测

    Args:
        model_path: 模型文件路径
        source: 输入源(图片/视频/目录/URL)
        **kwargs: 其他参数
    """
    print(f"🚀 YOLO11 目标检测 (单GPU)")
    print(f"📦 模型: {model_path}")
    print(f"📁 输入: {source}")

    try:
        # 加载模型
        model = YOLO(model_path)
        print("✅ 模型加载成功")

        # 执行预测 - 使用官方推荐参数
        results = model.predict(
            source=source,
            save=kwargs.get('save', True),
            save_txt=kwargs.get('save_txt', True),
            save_conf=kwargs.get('save_conf', True),
            conf=kwargs.get('conf', 0.25),
            iou=kwargs.get('iou', 0.45),
            imgsz=kwargs.get('imgsz', 640),
            device=kwargs.get('device', 'auto'),
            project=kwargs.get('project', 'runs/detect'),
            name=kwargs.get('name', 'exp'),
            exist_ok=kwargs.get('exist_ok', True),
            show=kwargs.get('show', False),
            verbose=kwargs.get('verbose', True)
        )

        print(f"✅ 检测完成! 结果保存在: runs/detect/{kwargs.get('name', 'exp')}/")
        return True

    except Exception as e:
        print(f"❌ 检测失败: {e}")
        return False

def detect_worker(gpu_id, model_path, source_batch, batch_id, **kwargs):
    """
    多GPU工作线程

    Args:
        gpu_id: GPU设备ID
        model_path: 模型路径
        source_batch: 输入源批次
        batch_id: 批次ID
        **kwargs: 其他参数
    """
    try:
        # 为每个线程创建独立的模型实例(线程安全)
        model = YOLO(model_path)

        # 设置GPU设备
        device = f"cuda:{gpu_id}" if torch.cuda.is_available() else "cpu"

        print(f"🔄 GPU-{gpu_id} 处理批次-{batch_id}: {len(source_batch)}个文件")

        # 执行预测
        results = model.predict(
            source=source_batch,
            device=device,
            save=kwargs.get('save', True),
            save_txt=kwargs.get('save_txt', True),
            save_conf=kwargs.get('save_conf', True),
            conf=kwargs.get('conf', 0.25),
            iou=kwargs.get('iou', 0.45),
            imgsz=kwargs.get('imgsz', 640),
            project=kwargs.get('project', 'runs/detect'),
            name=f"{kwargs.get('name', 'exp')}_gpu{gpu_id}_batch{batch_id}",
            exist_ok=True,
            verbose=False
        )

        print(f"✅ GPU-{gpu_id} 批次-{batch_id} 完成")
        return True

    except Exception as e:
        print(f"❌ GPU-{gpu_id} 批次-{batch_id} 失败: {e}")
        return False

def detect_multi_gpu(model_path, source, num_gpus=None, **kwargs):
    """
    多GPU并行检测

    Args:
        model_path: 模型文件路径
        source: 输入源目录
        num_gpus: 使用的GPU数量
        **kwargs: 其他参数
    """
    # 检查GPU可用性
    if not torch.cuda.is_available():
        print("❌ CUDA不可用，回退到单GPU模式")
        return detect_single_gpu(model_path, source, **kwargs)

    available_gpus = torch.cuda.device_count()
    if num_gpus is None:
        num_gpus = available_gpus
    else:
        num_gpus = min(num_gpus, available_gpus)

    if num_gpus <= 1:
        print("🔄 只有1个GPU可用，使用单GPU模式")
        return detect_single_gpu(model_path, source, **kwargs)

    print(f"🚀 YOLO11 多GPU并行检测")
    print(f"📦 模型: {model_path}")
    print(f"📁 输入: {source}")
    print(f"🎯 使用GPU数量: {num_gpus}/{available_gpus}")

    # 获取输入文件列表
    source_path = Path(source)
    if source_path.is_file():
        source_files = [str(source_path)]
    elif source_path.is_dir():
        # 支持常见图片和视频格式
        extensions = ['*.jpg', '*.jpeg', '*.png', '*.bmp', '*.tiff', '*.mp4', '*.avi', '*.mov', '*.mkv']
        source_files = []
        for ext in extensions:
            source_files.extend(source_path.glob(ext))
            source_files.extend(source_path.glob(ext.upper()))
        source_files = [str(f) for f in source_files]
    else:
        print(f"❌ 输入源无效: {source}")
        return False

    if not source_files:
        print(f"❌ 未找到有效的输入文件")
        return False

    print(f"📊 总文件数: {len(source_files)}")

    # 将文件分配到不同GPU
    batch_size = max(1, len(source_files) // num_gpus)
    batches = []
    for i in range(0, len(source_files), batch_size):
        batch = source_files[i:i + batch_size]
        if batch:  # 确保批次不为空
            batches.append(batch)

    # 限制批次数量不超过GPU数量
    if len(batches) > num_gpus:
        # 重新分配，确保每个GPU都有任务
        new_batches = [[] for _ in range(num_gpus)]
        for i, file in enumerate(source_files):
            new_batches[i % num_gpus].append(file)
        batches = [batch for batch in new_batches if batch]

    print(f"📦 分批情况: {len(batches)}个批次")
    for i, batch in enumerate(batches):
        print(f"  批次-{i}: {len(batch)}个文件 -> GPU-{i % num_gpus}")

    # 使用线程池执行多GPU推理
    success_count = 0
    with ThreadPoolExecutor(max_workers=num_gpus) as executor:
        futures = []
        for i, batch in enumerate(batches):
            gpu_id = i % num_gpus
            future = executor.submit(
                detect_worker,
                gpu_id,
                model_path,
                batch,
                i,
                **kwargs
            )
            futures.append(future)

        # 等待所有任务完成
        for future in as_completed(futures):
            if future.result():
                success_count += 1

    print(f"✅ 多GPU检测完成! 成功: {success_count}/{len(batches)}")
    return success_count == len(batches)

def main():
    """主函数"""
    parser = argparse.ArgumentParser(
        description="YOLO11 目标检测 - 支持TensorRT加速和多GPU并行",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
示例用法:
  # 基础检测
  python detect.py --source image.jpg                    # 检测单张图片
  python detect.py --source video.mp4                    # 检测视频
  python detect.py --source images/                      # 检测目录中所有图片
  python detect.py --source 0                            # 使用摄像头

  # TensorRT加速
  python detect.py --source images/ --tensorrt           # 自动导出并使用TensorRT
  python detect.py --source images/ --tensorrt --half    # 使用FP16精度
  python detect.py --source images/ --tensorrt --int8    # 使用INT8量化

  # 多GPU并行
  python detect.py --source images/ --multi-gpu          # 使用所有可用GPU
  python detect.py --source images/ --multi-gpu --num-gpus 2  # 使用2个GPU

  # 组合使用
  python detect.py --source images/ --tensorrt --multi-gpu --conf 0.5
        """
    )

    # 必需参数
    parser.add_argument('--source', '-s', required=True,
                       help='输入源: 图片/视频/目录/摄像头ID/URL')

    # 模型参数
    parser.add_argument('--model', '-m', default=None,
                       help='模型路径 (默认: 自动查找)')

    # 检测参数
    parser.add_argument('--conf', type=float, default=0.25,
                       help='置信度阈值 (默认: 0.25)')
    parser.add_argument('--iou', type=float, default=0.45,
                       help='NMS IoU阈值 (默认: 0.45)')
    parser.add_argument('--imgsz', type=int, default=640,
                       help='输入图像尺寸 (默认: 640)')
    parser.add_argument('--device', default='auto',
                       help='设备: auto/cpu/0/1/... (默认: auto)')

    # 输出参数
    parser.add_argument('--project', default='runs/detect',
                       help='输出项目目录 (默认: runs/detect)')
    parser.add_argument('--name', default='exp',
                       help='实验名称 (默认: exp)')
    parser.add_argument('--save-txt', action='store_true',
                       help='保存检测结果为txt文件')
    parser.add_argument('--save-conf', action='store_true',
                       help='保存置信度到txt文件')
    parser.add_argument('--show', action='store_true',
                       help='显示检测结果')
    parser.add_argument('--verbose', action='store_true', default=True,
                       help='详细输出')

    # TensorRT加速参数
    parser.add_argument('--tensorrt', action='store_true',
                       help='使用TensorRT加速推理')
    parser.add_argument('--half', action='store_true',
                       help='使用FP16精度 (TensorRT)')
    parser.add_argument('--int8', action='store_true',
                       help='使用INT8量化 (TensorRT)')
    parser.add_argument('--workspace', type=int, default=4,
                       help='TensorRT工作空间大小(GB) (默认: 4)')
    parser.add_argument('--data', default='coco.yaml',
                       help='数据集配置文件 (INT8量化需要)')

    # 多GPU参数
    parser.add_argument('--multi-gpu', action='store_true',
                       help='使用多GPU并行推理')
    parser.add_argument('--num-gpus', type=int, default=None,
                       help='使用的GPU数量 (默认: 所有可用GPU)')

    args = parser.parse_args()

    # 确定模型路径
    model_path = args.model or find_model()
    if not model_path:
        print("❌ 错误: 未找到可用模型")
        print("💡 请:")
        print("   1. 指定模型路径: --model path/to/model.pt")
        print("   2. 或下载预训练模型: yolo11n.pt")
        sys.exit(1)

    if not Path(model_path).exists():
        print(f"❌ 错误: 模型文件不存在: {model_path}")
        sys.exit(1)

    # 检查输入源(除了摄像头ID和URL)
    if not (args.source.isdigit() or args.source.startswith(('http://', 'https://'))):
        if not Path(args.source).exists():
            print(f"❌ 错误: 输入源不存在: {args.source}")
            sys.exit(1)

    # TensorRT加速处理
    if args.tensorrt:
        print("🔧 启用TensorRT加速...")

        # 检查是否已有TensorRT模型
        engine_path = str(Path(model_path).with_suffix('.engine'))
        if not Path(engine_path).exists():
            print("🔄 未找到TensorRT模型，开始导出...")
            engine_path = export_tensorrt(
                model_path,
                imgsz=args.imgsz,
                half=args.half,
                int8=args.int8,
                workspace=args.workspace,
                data=args.data,
                device=args.device if args.device != 'auto' else 0
            )
            if not engine_path:
                print("❌ TensorRT导出失败，使用原始模型")
            else:
                model_path = engine_path
        else:
            print(f"✅ 找到现有TensorRT模型: {engine_path}")
            model_path = engine_path

    # 执行检测
    if args.multi_gpu and Path(args.source).is_dir():
        # 多GPU模式(仅支持目录输入)
        success = detect_multi_gpu(
            model_path=model_path,
            source=args.source,
            num_gpus=args.num_gpus,
            conf=args.conf,
            iou=args.iou,
            imgsz=args.imgsz,
            project=args.project,
            name=args.name,
            save_txt=args.save_txt,
            save_conf=args.save_conf,
            show=args.show,
            verbose=args.verbose
        )
    else:
        # 单GPU模式
        if args.multi_gpu:
            print("⚠️  多GPU模式仅支持目录输入，回退到单GPU模式")

        success = detect_single_gpu(
            model_path=model_path,
            source=args.source,
            conf=args.conf,
            iou=args.iou,
            imgsz=args.imgsz,
            device=args.device,
            project=args.project,
            name=args.name,
            save_txt=args.save_txt,
            save_conf=args.save_conf,
            show=args.show,
            verbose=args.verbose
        )

    if not success:
        sys.exit(1)

if __name__ == "__main__":
    main()
