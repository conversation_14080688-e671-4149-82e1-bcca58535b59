#!/usr/bin/env python3
"""
YOLO11 极致性能检测脚本
专注于最高速度，最小开销
"""

from ultralytics import YOLO
import time
import argparse
import cv2
from pathlib import Path

def detect_video_ultra_fast(model_path, video_path, device='0'):
    """
    极致优化的视频检测
    """
    print(f"🚀 极速模式启动")
    print(f"📦 模型: {model_path}")
    print(f"📁 视频: {video_path}")
    
    # 加载模型
    model = YOLO(model_path)
    
    # 获取视频基本信息
    cap = cv2.VideoCapture(video_path)
    fps = cap.get(cv2.CAP_PROP_FPS) or 25
    total_frames = int(cap.get(cv2.CAP_PROP_FRAME_COUNT))
    video_duration = total_frames / fps
    cap.release()
    
    print(f"📹 视频信息: {total_frames}帧, {fps:.1f}FPS, {video_duration:.1f}秒")
    
    # 开始计时
    start_time = time.time()
    
    # 极简流式处理 - 移除所有不必要的功能
    results = model.predict(
        source=video_path,
        stream=True,        # 流式处理
        save=False,         # 不保存
        save_txt=False,     # 不保存txt
        save_conf=False,    # 不保存置信度
        save_crop=False,    # 不保存裁剪
        show=False,         # 不显示
        verbose=False,      # 不输出详细信息
        conf=0.25,          # 置信度阈值
        iou=0.45,           # NMS阈值
        device=device,      # 设备
        half=True,          # 使用FP16（如果支持）
        augment=False,      # 不使用数据增强
        agnostic_nms=False, # 不使用类别无关NMS
        max_det=100,        # 减少最大检测数以提升速度
        classes=None,       # 检测所有类别
        retina_masks=False, # 不使用高分辨率mask
        embed=None,         # 不返回特征向量
        imgsz=640,          # 固定输入尺寸
        vid_stride=1,       # 不跳帧，处理每一帧
    )
    
    # 极简统计
    frame_count = 0
    total_detections = 0
    
    # 处理结果 - 最小开销
    for result in results:
        frame_count += 1
        if result.boxes is not None:
            total_detections += len(result.boxes)
        
        # 每1000帧输出一次进度（减少输出频率）
        if frame_count % 1000 == 0:
            elapsed = time.time() - start_time
            fps_current = frame_count / elapsed
            progress = (frame_count / total_frames) * 100
            print(f"⚡ {progress:.1f}% ({frame_count}/{total_frames}) - {fps_current:.1f}帧/秒")
    
    # 计算最终统计
    total_time = time.time() - start_time
    processing_fps = frame_count / total_time
    
    print(f"\n🏁 处理完成!")
    print(f"   总帧数: {frame_count}")
    print(f"   总检测数: {total_detections}")
    print(f"   视频时长: {video_duration:.2f}秒")
    print(f"   处理耗时: {total_time:.2f}秒")
    print(f"   处理速度: {processing_fps:.1f}帧/秒")
    print(f"   实时倍速: {processing_fps/fps:.2f}x")
    
    return {
        'frame_count': frame_count,
        'total_detections': total_detections,
        'video_duration': video_duration,
        'processing_time': total_time,
        'processing_fps': processing_fps,
        'realtime_factor': processing_fps/fps
    }

def detect_video_skip_frames(model_path, video_path, device='0', skip_frames=2):
    """
    跳帧检测 - 更极端的速度优化
    """
    print(f"🚀 跳帧极速模式启动 (跳帧数: {skip_frames})")
    print(f"📦 模型: {model_path}")
    print(f"📁 视频: {video_path}")

    # 加载模型
    model = YOLO(model_path)

    # 获取视频基本信息
    cap = cv2.VideoCapture(video_path)
    fps = cap.get(cv2.CAP_PROP_FPS) or 25
    total_frames = int(cap.get(cv2.CAP_PROP_FRAME_COUNT))
    video_duration = total_frames / fps
    cap.release()

    print(f"📹 视频信息: {total_frames}帧, {fps:.1f}FPS, {video_duration:.1f}秒")
    print(f"⚡ 将处理约 {total_frames//(skip_frames+1)} 帧")

    # 开始计时
    start_time = time.time()

    # 跳帧处理
    results = model.predict(
        source=video_path,
        stream=True,
        save=False,
        save_txt=False,
        save_conf=False,
        save_crop=False,
        show=False,
        verbose=False,
        conf=0.3,           # 提高置信度阈值减少检测数量
        iou=0.5,            # 提高IOU阈值减少重复检测
        device=device,
        half=True,
        augment=False,
        agnostic_nms=False,
        max_det=100,         # 进一步减少最大检测数
        classes=None,
        retina_masks=False,
        embed=None,
        imgsz=640,
        vid_stride=skip_frames+1,  # 跳帧处理
    )

    # 极简统计
    frame_count = 0
    total_detections = 0

    # 处理结果
    for result in results:
        frame_count += 1
        if result.boxes is not None:
            total_detections += len(result.boxes)

        # 每500帧输出一次进度
        if frame_count % 500 == 0:
            elapsed = time.time() - start_time
            fps_current = frame_count / elapsed
            actual_frame = frame_count * (skip_frames + 1)
            progress = (actual_frame / total_frames) * 100
            print(f"⚡ {progress:.1f}% (处理{frame_count}帧/实际{actual_frame}帧) - {fps_current:.1f}帧/秒")

    # 计算最终统计
    total_time = time.time() - start_time
    processing_fps = frame_count / total_time
    actual_frames_processed = frame_count * (skip_frames + 1)

    print(f"\n🏁 跳帧处理完成!")
    print(f"   处理帧数: {frame_count}")
    print(f"   实际覆盖: {actual_frames_processed}/{total_frames}帧")
    print(f"   总检测数: {total_detections}")
    print(f"   视频时长: {video_duration:.2f}秒")
    print(f"   处理耗时: {total_time:.2f}秒")
    print(f"   处理速度: {processing_fps:.1f}帧/秒")
    print(f"   等效速度: {processing_fps*(skip_frames+1):.1f}帧/秒")
    print(f"   实时倍速: {(processing_fps*(skip_frames+1))/fps:.2f}x")

def main():
    parser = argparse.ArgumentParser(description='YOLO11 极速检测')
    parser.add_argument('--model', '-m', required=True, help='模型路径')
    parser.add_argument('--source', '-s', required=True, help='视频路径')
    parser.add_argument('--device', '-d', default='0', help='设备 (0, cpu)')
    parser.add_argument('--skip-frames', type=int, default=0, help='跳帧数 (0=不跳帧, 2=每3帧处理1帧)')

    args = parser.parse_args()
    
    # 检查文件存在
    if not Path(args.model).exists():
        print(f"❌ 模型文件不存在: {args.model}")
        return
    
    if not Path(args.source).exists():
        print(f"❌ 视频文件不存在: {args.source}")
        return
    
    # 执行检测
    try:
        if args.skip_frames > 0:
            detect_video_skip_frames(args.model, args.source, args.device, args.skip_frames)
        else:
            detect_video_ultra_fast(args.model, args.source, args.device)
    except Exception as e:
        print(f"❌ 检测失败: {e}")

if __name__ == "__main__":
    main()
