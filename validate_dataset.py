#!/usr/bin/env python3
"""
数据集验证脚本
检查数据集质量，统计有标注和无标注的图片
"""

import os
from pathlib import Path
import matplotlib.pyplot as plt
import cv2
import numpy as np

def analyze_dataset(source_dir):
    """分析原始数据集"""
    source_path = Path(source_dir)
    image_dir = source_path / "picture"
    
    if not image_dir.exists():
        print(f"错误: 图片目录不存在 {image_dir}")
        return
    
    # 获取所有图片文件
    image_files = list(image_dir.glob("*.jpg"))
    print(f"总图片数量: {len(image_files)}")
    
    # 统计有标注和无标注的图片
    labeled_images = []
    unlabeled_images = []
    
    for img_file in image_files:
        label_name = img_file.stem + ".txt"
        label_path = source_path / label_name
        
        if label_path.exists() and label_path.stat().st_size > 0:
            labeled_images.append(img_file)
        else:
            unlabeled_images.append(img_file)
    
    print(f"有标注图片: {len(labeled_images)}")
    print(f"无标注图片: {len(unlabeled_images)}")
    print(f"标注覆盖率: {len(labeled_images)/len(image_files)*100:.1f}%")
    
    return labeled_images, unlabeled_images

def check_label_quality(source_dir, sample_size=10):
    """检查标注质量"""
    source_path = Path(source_dir)
    image_dir = source_path / "picture"
    
    # 获取有标注的图片
    labeled_images = []
    for img_file in image_dir.glob("*.jpg"):
        label_name = img_file.stem + ".txt"
        label_path = source_path / label_name
        if label_path.exists() and label_path.stat().st_size > 0:
            labeled_images.append(img_file)
    
    if len(labeled_images) == 0:
        print("没有找到有标注的图片")
        return
    
    # 随机选择样本检查
    import random
    random.seed(42)
    sample_images = random.sample(labeled_images, min(sample_size, len(labeled_images)))
    
    print(f"\n检查 {len(sample_images)} 张图片的标注质量:")
    
    for i, img_file in enumerate(sample_images):
        print(f"\n{i+1}. {img_file.name}")
        
        # 读取图片
        img = cv2.imread(str(img_file))
        if img is None:
            print(f"  错误: 无法读取图片")
            continue
        
        h, w = img.shape[:2]
        
        # 读取标注
        label_name = img_file.stem + ".txt"
        label_path = source_path / label_name
        
        try:
            with open(label_path, 'r') as f:
                lines = f.readlines()
            
            print(f"  图片尺寸: {w}x{h}")
            print(f"  标注数量: {len(lines)}")
            
            for j, line in enumerate(lines):
                parts = line.strip().split()
                if len(parts) >= 5:
                    cls, x_center, y_center, width, height = map(float, parts[:5])
                    print(f"    目标{j+1}: 类别={int(cls)}, 中心=({x_center:.3f},{y_center:.3f}), 尺寸=({width:.3f},{height:.3f})")
                    
                    # 检查坐标是否在合理范围内
                    if not (0 <= x_center <= 1 and 0 <= y_center <= 1 and 0 < width <= 1 and 0 < height <= 1):
                        print(f"    警告: 坐标超出范围!")
                else:
                    print(f"    错误: 标注格式不正确")
                    
        except Exception as e:
            print(f"  错误: 读取标注文件失败 - {e}")

def visualize_samples(source_dir, num_samples=5):
    """可视化标注样本"""
    source_path = Path(source_dir)
    image_dir = source_path / "picture"
    
    # 获取有标注的图片
    labeled_images = []
    for img_file in image_dir.glob("*.jpg"):
        label_name = img_file.stem + ".txt"
        label_path = source_path / label_name
        if label_path.exists() and label_path.stat().st_size > 0:
            labeled_images.append(img_file)
    
    if len(labeled_images) == 0:
        print("没有找到有标注的图片")
        return
    
    # 随机选择样本
    import random
    random.seed(42)
    sample_images = random.sample(labeled_images, min(num_samples, len(labeled_images)))
    
    fig, axes = plt.subplots(1, len(sample_images), figsize=(15, 3))
    if len(sample_images) == 1:
        axes = [axes]
    
    for i, img_file in enumerate(sample_images):
        # 读取图片
        img = cv2.imread(str(img_file))
        img_rgb = cv2.cvtColor(img, cv2.COLOR_BGR2RGB)
        h, w = img.shape[:2]
        
        # 读取标注
        label_name = img_file.stem + ".txt"
        label_path = source_path / label_name
        
        try:
            with open(label_path, 'r') as f:
                lines = f.readlines()
            
            # 绘制边界框
            for line in lines:
                parts = line.strip().split()
                if len(parts) >= 5:
                    cls, x_center, y_center, width, height = map(float, parts[:5])
                    
                    # 转换为像素坐标
                    x1 = int((x_center - width/2) * w)
                    y1 = int((y_center - height/2) * h)
                    x2 = int((x_center + width/2) * w)
                    y2 = int((y_center + height/2) * h)
                    
                    # 绘制矩形
                    cv2.rectangle(img_rgb, (x1, y1), (x2, y2), (255, 0, 0), 2)
                    cv2.putText(img_rgb, f'pig', (x1, y1-10), cv2.FONT_HERSHEY_SIMPLEX, 0.5, (255, 0, 0), 1)
        
        except Exception as e:
            print(f"处理 {img_file.name} 时出错: {e}")
        
        axes[i].imshow(img_rgb)
        axes[i].set_title(f'{img_file.name}')
        axes[i].axis('off')
    
    plt.tight_layout()
    plt.savefig('dataset_samples.png', dpi=150, bbox_inches='tight')
    plt.show()
    print("样本可视化已保存为 dataset_samples.png")

def main():
    """主函数"""
    print("=== 数据集验证工具 ===")
    
    source_dir = "output-dataset"
    
    if not Path(source_dir).exists():
        print(f"错误: 源数据集目录不存在: {source_dir}")
        return
    
    # 分析数据集
    print("\n1. 数据集统计:")
    labeled_images, unlabeled_images = analyze_dataset(source_dir)
    
    # 检查标注质量
    print("\n2. 标注质量检查:")
    check_label_quality(source_dir, sample_size=5)
    
    # 可视化样本
    print("\n3. 生成可视化样本:")
    try:
        visualize_samples(source_dir, num_samples=5)
    except Exception as e:
        print(f"可视化失败: {e}")
    
    # 给出建议
    print("\n=== 建议 ===")
    if len(unlabeled_images) > 0:
        print(f"发现 {len(unlabeled_images)} 张无标注图片")
        print("建议:")
        print("1. 检查这些图片是否包含猪")
        print("2. 如果包含猪，请补充标注")
        print("3. 如果不包含猪，可以作为负样本保留")
        print("4. 或者使用 only_labeled=True 参数只训练有标注的图片")
    
    print(f"\n有效训练数据: {len(labeled_images)} 张")
    if len(labeled_images) < 500:
        print("警告: 训练数据较少，建议:")
        print("- 增加数据增强")
        print("- 减少训练轮数避免过拟合")
        print("- 使用较小的学习率")

if __name__ == "__main__":
    main()
