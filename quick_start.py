#!/usr/bin/env python3
"""
YOLO11x 猪检测快速开始脚本
简化版训练和推理示例
"""

from ultralytics import YOLO
import os

def quick_train():
    """快速训练示例"""
    print("=== YOLO11x 猪检测快速训练 ===")
    
    # 检查数据集是否存在
    if not os.path.exists("pig_dataset.yaml"):
        print("错误: 未找到数据集配置文件 pig_dataset.yaml")
        print("请先按照 README_PIG_TRAINING.md 准备数据集")
        return
    
    if not os.path.exists("pig_dataset"):
        print("错误: 未找到数据集目录 pig_dataset")
        print("请先按照 README_PIG_TRAINING.md 准备数据集")
        return
    
    # 加载预训练模型
    model = YOLO('yolo11x.pt')
    
    # 开始训练 (简化参数)
    print("开始训练...")
    results = model.train(
        data='pig_dataset.yaml',    # 数据集配置
        epochs=50,                  # 训练轮数
        imgsz=640,                  # 图像尺寸
        batch=8,                    # 批次大小
        name='pig_detection_quick', # 实验名称
        single_cls=True,            # 单类别
        device='auto'               # 自动选择设备
    )
    
    print("训练完成!")
    print("模型保存在: runs/detect/pig_detection_quick/weights/best.pt")

def quick_predict():
    """快速推理示例"""
    print("=== YOLO11x 猪检测快速推理 ===")
    
    # 检查模型是否存在
    model_path = "runs/detect/pig_detection_quick/weights/best.pt"
    if not os.path.exists(model_path):
        print(f"错误: 未找到训练好的模型 {model_path}")
        print("请先运行训练或使用其他模型路径")
        return
    
    # 加载模型
    model = YOLO(model_path)
    
    # 示例推理 (需要提供图片路径)
    image_path = input("请输入要检测的图片路径: ").strip()
    
    if not os.path.exists(image_path):
        print(f"错误: 图片文件 {image_path} 不存在")
        return
    
    # 进行预测
    results = model(image_path)
    
    # 显示结果
    for result in results:
        result.show()  # 显示图片
        result.save(filename='prediction_result.jpg')  # 保存结果
        
        # 打印检测信息
        if result.boxes is not None:
            print(f"检测到 {len(result.boxes)} 只猪")
            for i, box in enumerate(result.boxes):
                conf = box.conf[0].item()
                print(f"猪 {i+1}: 置信度 {conf:.3f}")
        else:
            print("未检测到猪")

def main():
    """主函数"""
    print("YOLO11x 猪检测快速开始")
    print("1. 训练模型")
    print("2. 推理测试")
    print("3. 退出")
    
    while True:
        choice = input("\n请选择操作 (1/2/3): ").strip()
        
        if choice == '1':
            quick_train()
        elif choice == '2':
            quick_predict()
        elif choice == '3':
            print("退出程序")
            break
        else:
            print("无效选择，请输入 1、2 或 3")

if __name__ == "__main__":
    main()
