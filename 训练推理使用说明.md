# YOLO11x 猪检测训练推理系统

## 系统概述

这是一个完整的YOLO11x猪检测训练推理系统，包含数据集处理、模型训练和推理功能。

## 文件说明

- `prepare_dataset.py`: 数据集处理脚本，将output-dataset转换为YOLO训练格式
- `train.py`: 模型训练脚本，使用yolo11x.pt预训练模型
- `detect.py`: 推理脚本，使用训练好的模型进行检测
- `pig_dataset.yaml`: 数据集配置文件
- `训练推理使用说明.md`: 本说明文件

## 使用流程

### 第1步: 准备数据集

```bash
python prepare_dataset.py
```

这将：
- 将output-dataset中的554张图片按80%:20%分割为训练集和验证集
- 创建标准的YOLO目录结构：
  ```
  pig_dataset/
  ├── images/
  │   ├── train/    # 443张训练图片
  │   └── val/      # 111张验证图片
  └── labels/
      ├── train/    # 443个训练标签
      └── val/      # 111个验证标签
  ```

### 第2步: 训练模型

```bash
python train.py
```

训练配置：
- 预训练模型: yolo11x.pt
- 训练轮数: 100 epochs
- 图像尺寸: 640x640
- 批次大小: 16
- 自动选择GPU/CPU

训练结果保存在：
- `runs/train/pig_detection/weights/best.pt` (最佳模型)
- `runs/train/pig_detection/weights/last.pt` (最后模型)
- `runs/train/pig_detection/` (训练日志和图表)

### 第3步: 推理检测

```bash
# 使用默认设置（检测验证集）
python detect.py

# 检测指定图片/目录
python detect.py --source path/to/images

# 使用指定模型
python detect.py --model runs/train/pig_detection/weights/best.pt --source test_images/
```

推理结果保存在：
- `runs/detect/pig_detection/` (检测结果图片)
- `runs/detect/pig_detection/labels/` (检测结果文本)

## 详细参数说明

### 训练参数 (train.py)
- `epochs`: 100 - 训练轮数
- `imgsz`: 640 - 输入图像尺寸
- `batch`: 16 - 批次大小
- `conf`: 0.25 - 置信度阈值
- `device`: auto - 自动选择设备

### 推理参数 (detect.py)
- `--source`: 输入图片/目录路径
- `--model`: 模型文件路径
- `--output`: 输出目录
- `conf`: 0.25 - 置信度阈值
- `iou`: 0.45 - NMS IoU阈值

## 预期结果

### 数据集统计
- 训练集: 443张图片，约14,112个标注框
- 验证集: 111张图片，约3,528个标注框
- 平均每张图片: 31.84个猪标注框

### 训练时间估算
- GPU (RTX 3080): 约2-4小时
- GPU (RTX 4090): 约1-2小时
- CPU: 约10-20小时

### 模型性能指标
训练完成后可查看：
- mAP@0.5: 平均精度
- Precision: 精确率
- Recall: 召回率
- F1-Score: F1分数

## 故障排除

### 常见问题

1. **缺少预训练模型**
   ```
   错误: 缺少以下必需文件/目录: yolo11x.pt
   ```
   解决：确保yolo11x.pt文件在项目根目录

2. **数据集未准备**
   ```
   错误: 缺少以下必需文件/目录: pig_dataset
   ```
   解决：先运行 `python prepare_dataset.py`

3. **内存不足**
   ```
   CUDA out of memory
   ```
   解决：在train.py中减小batch参数（如改为8或4）

4. **GPU不可用**
   ```
   No CUDA devices available
   ```
   解决：系统会自动使用CPU，训练时间会更长

### 性能优化建议

1. **提高训练速度**
   - 使用更大的batch size（如果GPU内存允许）
   - 使用多GPU训练
   - 使用SSD存储数据集

2. **提高模型精度**
   - 增加训练轮数（epochs）
   - 使用数据增强
   - 调整学习率

3. **减少内存使用**
   - 减小batch size
   - 减小图像尺寸（imgsz）
   - 使用更小的模型（如yolo11s.pt）

## 高级用法

### 自定义训练参数
编辑train.py中的train_args字典：
```python
train_args = {
    'epochs': 200,        # 增加训练轮数
    'batch': 32,          # 增加批次大小
    'lr0': 0.01,          # 设置初始学习率
    'augment': True,      # 启用数据增强
}
```

### 模型验证
```bash
# 验证最佳模型
python -m ultralytics val model=runs/train/pig_detection/weights/best.pt data=pig_dataset.yaml

# 在测试集上评估
python -m ultralytics val model=runs/train/pig_detection/weights/best.pt data=pig_dataset.yaml split=test
```

### 模型导出
```bash
# 导出为ONNX格式
python -m ultralytics export model=runs/train/pig_detection/weights/best.pt format=onnx

# 导出为TensorRT格式
python -m ultralytics export model=runs/train/pig_detection/weights/best.pt format=engine
```

## 注意事项

1. **硬件要求**: 建议使用GPU进行训练，CPU训练会非常慢
2. **存储空间**: 确保有足够的磁盘空间存储数据集和训练结果
3. **训练时间**: 完整训练需要较长时间，建议在稳定的环境中运行
4. **模型保存**: 训练过程中会自动保存检查点，可以随时中断和恢复

祝您训练顺利！🐷
