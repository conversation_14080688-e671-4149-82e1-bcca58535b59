# YOLO11x 单类别猪检测训练指南

本指南将帮助您使用YOLO11x预训练模型训练一个专门检测猪的目标检测模型。

## 📁 项目结构

```
ultralytics-main/
├── yolo11x.pt                    # 预训练模型 (已存在)
├── pig_dataset.yaml              # 数据集配置文件
├── train_pig_detection.py        # 训练脚本
├── predict_pig.py                # 推理脚本
├── README_PIG_TRAINING.md         # 本指南
└── pig_dataset/                   # 数据集目录 (需要创建)
    ├── images/
    │   ├── train/                 # 训练图片
    │   └── val/                   # 验证图片
    └── labels/
        ├── train/                 # 训练标签
        └── val/                   # 验证标签
```

## 🗂️ 数据集准备

### 1. 创建数据集目录结构

```bash
mkdir -p pig_dataset/images/train
mkdir -p pig_dataset/images/val
mkdir -p pig_dataset/labels/train
mkdir -p pig_dataset/labels/val
```

### 2. 准备图片数据

- 将训练图片放入 `pig_dataset/images/train/` 目录
- 将验证图片放入 `pig_dataset/images/val/` 目录
- 建议训练集和验证集比例为 8:2 或 7:3
- 图片格式支持: `.jpg`, `.jpeg`, `.png`, `.bmp`

### 3. 准备标注数据

每张图片都需要对应的标注文件，标注文件格式为YOLO格式：

#### 标注文件命名规则
- 图片文件: `image001.jpg`
- 标注文件: `image001.txt`

#### YOLO标注格式
每行表示一个目标，格式为：
```
class_id center_x center_y width height
```

对于单类别猪检测：
- `class_id`: 固定为 0 (猪的类别ID)
- `center_x`: 边界框中心点x坐标 (相对于图片宽度，范围0-1)
- `center_y`: 边界框中心点y坐标 (相对于图片高度，范围0-1)
- `width`: 边界框宽度 (相对于图片宽度，范围0-1)
- `height`: 边界框高度 (相对于图片高度，范围0-1)

#### 示例标注文件内容
```
0 0.5 0.4 0.3 0.6
0 0.2 0.7 0.15 0.25
```
表示图片中有2只猪。

### 4. 标注工具推荐

- **LabelImg**: 图形化标注工具，支持YOLO格式导出
  ```bash
  pip install labelImg
  labelImg
  ```

- **Roboflow**: 在线标注平台，支持多种格式导出
  - 网址: https://roboflow.com/

- **CVAT**: 开源标注工具
  - 网址: https://cvat.ai/

## 🚀 训练模型

### 1. 检查环境

确保已安装必要的依赖：
```bash
pip install ultralytics torch torchvision opencv-python
```

### 2. 开始训练

```bash
python train_pig_detection.py
```

### 3. 训练参数说明

主要训练参数（可在 `train_pig_detection.py` 中修改）：

- `epochs`: 训练轮数 (默认100)
- `batch`: 批次大小 (默认16，根据GPU内存调整)
- `imgsz`: 输入图像尺寸 (默认640)
- `lr0`: 初始学习率 (默认0.01)
- `conf`: 置信度阈值 (默认0.25)

### 4. 监控训练过程

训练过程中会生成以下文件：
- `runs/detect/pig_detection/weights/best.pt`: 最佳模型
- `runs/detect/pig_detection/weights/last.pt`: 最后一轮模型
- `runs/detect/pig_detection/results.png`: 训练曲线图
- `runs/detect/pig_detection/confusion_matrix.png`: 混淆矩阵

## 🔍 模型推理

### 1. 单张图片预测

```bash
python predict_pig.py --source path/to/image.jpg --model runs/detect/pig_detection/weights/best.pt
```

### 2. 批量图片预测

```bash
python predict_pig.py --source path/to/images_folder --model runs/detect/pig_detection/weights/best.pt
```

### 3. 视频预测

```bash
python predict_pig.py --source path/to/video.mp4 --model runs/detect/pig_detection/weights/best.pt
```

### 4. 调整置信度阈值

```bash
python predict_pig.py --source path/to/image.jpg --conf 0.5 --model runs/detect/pig_detection/weights/best.pt
```

## 📊 模型评估

### 1. 验证模型性能

```python
from ultralytics import YOLO

# 加载训练好的模型
model = YOLO('runs/detect/pig_detection/weights/best.pt')

# 在验证集上评估
metrics = model.val(data='pig_dataset.yaml')

print(f"mAP50: {metrics.box.map50}")
print(f"mAP50-95: {metrics.box.map}")
```

### 2. 关键指标说明

- **mAP50**: 在IoU=0.5时的平均精度
- **mAP50-95**: 在IoU=0.5-0.95时的平均精度
- **Precision**: 精确率
- **Recall**: 召回率

## 🛠️ 常见问题

### 1. GPU内存不足

- 减小 `batch` 大小 (如改为8或4)
- 减小 `imgsz` (如改为416)

### 2. 训练收敛慢

- 增加训练数据量
- 调整学习率
- 使用数据增强

### 3. 检测精度不高

- 增加训练轮数
- 改善标注质量
- 增加训练数据的多样性

### 4. 标注格式错误

确保标注文件：
- 每行格式正确: `class_id x y w h`
- 坐标值在0-1范围内
- 文件名与图片对应

## 📈 优化建议

### 1. 数据集质量

- 确保标注准确
- 包含不同角度、光照、背景的猪图片
- 训练集至少包含500-1000张图片

### 2. 训练策略

- 使用预训练模型进行迁移学习
- 适当的数据增强
- 早停策略防止过拟合

### 3. 模型部署

- 导出为ONNX格式提高推理速度
- 使用TensorRT加速GPU推理
- 量化模型减小文件大小

## 🎯 下一步

1. 准备您的猪图片数据集
2. 使用标注工具创建YOLO格式标注
3. 运行训练脚本
4. 评估模型性能
5. 使用训练好的模型进行推理

祝您训练成功！🐷
