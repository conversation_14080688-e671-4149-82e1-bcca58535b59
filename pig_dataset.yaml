# Ultralytics YOLO11 单类别猪数据集配置文件
# 使用方法: yolo train data=pig_dataset.yaml model=yolo11x.pt

# 数据集根目录和子目录结构
# pig_dataset/
# ├── images/
# │   ├── train/          # 训练图片
# │   └── val/            # 验证图片
# └── labels/
#     ├── train/          # 训练标签 (.txt格式)
#     └── val/            # 验证标签 (.txt格式)

# 数据集路径配置
path: pig_dataset  # 数据集根目录
train: images/train  # 训练图片路径 (相对于path)
val: images/val      # 验证图片路径 (相对于path)
test:                # 测试图片路径 (可选)

# 类别定义 - 单类别猪
names:
  0: pig

# 数据集信息
nc: 1  # 类别数量
