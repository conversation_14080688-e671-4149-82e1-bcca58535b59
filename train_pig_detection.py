#!/usr/bin/env python3
"""
YOLO11x 单类别猪检测训练脚本
使用预训练的yolo11x.pt模型进行迁移学习
"""

import os
from ultralytics import YOLO
import torch

def main():
    """主训练函数"""
    
    # 检查GPU可用性
    device = 'cuda' if torch.cuda.is_available() else 'cpu'
    print(f"使用设备: {device}")
    
    # 检查预训练模型是否存在
    model_path = "yolo11x.pt"
    if not os.path.exists(model_path):
        print(f"预训练模型 {model_path} 不存在，将自动下载...")
    
    # 检查数据集配置文件是否存在
    data_config = "pig_dataset.yaml"
    if not os.path.exists(data_config):
        print(f"错误: 数据集配置文件 {data_config} 不存在!")
        print("请确保已创建数据集配置文件和数据集目录结构")
        return
    
    # 检查数据集目录是否存在
    dataset_path = "pig_dataset"
    if not os.path.exists(dataset_path):
        print(f"错误: 数据集目录 {dataset_path} 不存在!")
        print("请按照README.md中的说明准备数据集")
        return
    
    # 加载预训练模型
    print("加载YOLO11x预训练模型...")
    model = YOLO(model_path)
    
    # 训练参数配置 - 针对1000张图片优化
    train_args = {
        'data': data_config,           # 数据集配置文件
        'epochs': 80,                  # 训练轮数 (小数据集减少epoch避免过拟合)
        'patience': 15,                # 早停耐心值 (15个epoch无改善则停止)
        'imgsz': 640,                  # 输入图像尺寸
        'batch': 16,                   # 批次大小 (根据GPU内存调整)
        'lr0': 0.003,                  # 初始学习率 (降低学习率)
        'lrf': 0.01,                   # 最终学习率 (lr0 * lrf)
        'momentum': 0.937,             # SGD动量
        'weight_decay': 0.0005,        # 权重衰减
        'warmup_epochs': 3.0,          # 预热轮数
        'warmup_momentum': 0.8,        # 预热动量
        'warmup_bias_lr': 0.1,         # 预热偏置学习率
        'box': 7.5,                    # 边界框损失权重
        'cls': 0.5,                    # 分类损失权重
        'dfl': 1.5,                    # DFL损失权重
        'pose': 12.0,                  # 姿态损失权重
        'kobj': 1.0,                   # 关键点obj损失权重
        'label_smoothing': 0.0,        # 标签平滑
        'nbs': 64,                     # 标准批次大小
        'overlap_mask': True,          # 训练期间掩码应该重叠
        'mask_ratio': 4,               # 掩码下采样比率
        'dropout': 0.0,                # 使用dropout正则化
        'val': True,                   # 训练期间验证
        'plots': True,                 # 保存训练图表
        'save': True,                  # 保存检查点
        'save_period': 10,             # 每10轮保存检查点
        'save_best': True,             # 保存最佳模型
        'cache': False,                # 缓存图像以加快训练速度
        'device': device,              # 训练设备
        'workers': 8,                  # 数据加载器工作进程数
        'project': 'runs/detect',      # 项目名称
        'name': 'pig_detection',       # 实验名称
        'exist_ok': True,              # 允许覆盖现有实验
        'pretrained': True,            # 使用预训练权重
        'optimizer': 'auto',           # 优化器 (auto, SGD, Adam, AdamW)
        'verbose': True,               # 详细输出
        'seed': 0,                     # 随机种子
        'deterministic': True,         # 确定性训练
        'single_cls': True,            # 单类别训练
        'rect': False,                 # 矩形训练
        'cos_lr': False,               # 余弦学习率调度器
        'close_mosaic': 10,            # 在最后N轮关闭马赛克增强
        'resume': False,               # 从上次训练恢复
        'amp': True,                   # 自动混合精度训练
        'fraction': 1.0,               # 使用数据集的比例
        'profile': False,              # 分析ONNX和TensorRT速度
        'freeze': None,                # 冻结层 (int or list, optional)
        'multi_scale': False,          # 多尺度训练
        # 数据增强参数 (针对小数据集优化)
        'hsv_h': 0.015,                # 色调增强
        'hsv_s': 0.7,                  # 饱和度增强
        'hsv_v': 0.4,                  # 明度增强
        'degrees': 10.0,               # 旋转角度
        'translate': 0.1,              # 平移比例
        'scale': 0.5,                  # 缩放比例
        'shear': 2.0,                  # 剪切角度
        'perspective': 0.0,            # 透视变换
        'flipud': 0.0,                 # 上下翻转概率
        'fliplr': 0.5,                 # 左右翻转概率
        'mosaic': 1.0,                 # 马赛克增强概率
        'mixup': 0.1,                  # 混合增强概率
        'copy_paste': 0.1,             # 复制粘贴增强概率
    }
    
    print("开始训练...")
    print(f"训练参数: {train_args}")
    
    # 开始训练
    try:
        results = model.train(**train_args)
        print("训练完成!")
        print(f"最佳模型保存在: runs/detect/pig_detection/weights/best.pt")
        print(f"最后模型保存在: runs/detect/pig_detection/weights/last.pt")
        
        # 在验证集上评估最佳模型
        print("\n在验证集上评估最佳模型...")
        best_model = YOLO('runs/detect/pig_detection/weights/best.pt')
        metrics = best_model.val(data=data_config)
        print(f"验证结果: {metrics}")
        
    except Exception as e:
        print(f"训练过程中出现错误: {e}")
        print("请检查数据集路径和格式是否正确")

if __name__ == "__main__":
    main()
