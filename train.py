#!/usr/bin/env python3
"""
YOLO11x 猪检测模型训练脚本
使用预训练的yolo11x.pt模型进行单类别猪检测训练
"""

from ultralytics import YOLO
import os
from pathlib import Path
import torch

def check_requirements():
    """检查训练所需的文件和目录"""
    required_files = [
        "yolo11x.pt",           # 预训练模型
        "pig_dataset.yaml",     # 数据集配置文件
        "pig_dataset"           # 数据集目录
    ]
    
    missing_files = []
    for file_path in required_files:
        if not Path(file_path).exists():
            missing_files.append(file_path)
    
    if missing_files:
        print("错误: 缺少以下必需文件/目录:")
        for file_path in missing_files:
            print(f"  - {file_path}")
        print("\n请确保:")
        print("1. yolo11x.pt 预训练模型文件存在")
        print("2. pig_dataset.yaml 配置文件存在")
        print("3. 已运行 prepare_dataset.py 生成 pig_dataset 目录")
        return False
    
    return True

def train_model():

    # 检查GPU可用性
    device = 'cuda' if torch.cuda.is_available() else 'cpu'
    print(f"使用设备: {device}")

    """训练YOLO11x模型"""
    print("=== YOLO11x 猪检测模型训练 ===")
    
    # 检查必需文件
    if not check_requirements():
        return
    
    # 加载预训练模型
    print("加载预训练模型: yolo11x.pt")
    model = YOLO("yolo11x.pt")
    
    # 训练参数配置
    train_args = {
        'data': 'pig_dataset.yaml',    # 数据集配置文件
        'epochs': 100,                 # 训练轮数
        'imgsz': 640,                  # 输入图像尺寸
        'batch': 16,                   # 批次大小
        'workers': 8,                  # 数据加载线程数
        'device': device,              # 自动选择设备
        'project': 'runs/train',       # 项目目录
        'name': 'pig_detection',       # 实验名称
        'save': True,                  # 保存检查点
        'save_period': 10,             # 每10个epoch保存一次
        'val': True,                   # 启用验证
        'plots': True,                 # 生成训练图表
        'verbose': True,               # 详细输出
    }
    
    print("\n训练配置:")
    for key, value in train_args.items():
        print(f"  {key}: {value}")
    
    print(f"\n开始训练...")
    print("注意: 训练过程可能需要较长时间，请耐心等待")
    print("训练结果将保存在 runs/train/pig_detection/ 目录中")
    
    try:
        # 开始训练
        results = model.train(**train_args)
        
        print(f"\n训练完成!")
        print(f"最佳模型保存在: runs/train/pig_detection/weights/best.pt")
        print(f"最后模型保存在: runs/train/pig_detection/weights/last.pt")
        print(f"训练日志和图表保存在: runs/train/pig_detection/")
        
        return True
        
    except Exception as e:
        print(f"训练过程中出现错误: {e}")
        return False

def main():
    """主函数"""
    success = train_model()
    
    if success:
        print("\n=== 训练完成 ===")
        print("接下来您可以:")
        print("1. 查看训练结果: runs/train/pig_detection/")
        print("2. 使用训练好的模型进行推理: python detect.py")
        print("3. 验证模型性能: python -m ultralytics val model=runs/train/pig_detection/weights/best.pt")
    else:
        print("\n训练失败，请检查错误信息并重试")

if __name__ == "__main__":
    main()
