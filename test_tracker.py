#!/usr/bin/env python3
"""
YOLOv11 猪检测智能推流脚本 (v3.0 - 个体追踪版)

功能特性:
- 输入: RTMP, RTSP, 摄像头等视频流
- 输出: 稳定的HLS (m3u8) 直播流
- 个体追踪: 对每头猪进行独立ID追踪和计数
- 智能统计: 每头猪需连续出现min_consecutive_frames帧才算有效
- 分辨率优化: 等比缩放至1280px长边
- 帧率同步与稳定性监控
- 周期性报告: 每15秒通过API上报JSON数据
- 快照优化: 仅保留最新的单张快照图片
- 数据上报: 图片以Base64格式随JSON一同POST到远程服务器
"""

import os
import argparse
import threading
import subprocess
import time
import socket
import json
import base64
from collections import deque, defaultdict
from datetime import datetime

from ultralytics import YOLO
import cv2
import requests
from flask import Flask, send_file, render_template_string
from flask_cors import CORS

# --- API 上报配置 ---
# !!! 重要: 请将此URL替换为您真实的API接收端点 !!!
API_ENDPOINT = "http://***************:48902/api/AnalysisResult/Push"

# --- 辅助函数 ---

def get_local_ip():
    """获取本机局域网IP地址，以便生成可访问的URL。"""
    s = socket.socket(socket.AF_INET, socket.SOCK_DGRAM)
    try:
        s.connect(('**************', 1))
        IP = s.getsockname()[0]
    except Exception:
        IP = '127.0.0.1'
    finally:
        s.close()
    return IP

def draw_boxes_with_ids(image, boxes, track_ids=None, thickness=2):
    """在图像上绘制边界框和追踪ID。"""
    if boxes is None:
        return image
    annotated_img = image.copy()
    for i, box in enumerate(boxes):
        x1, y1, x2, y2 = box.xyxy[0].cpu().numpy().astype(int)
        # 绘制边界框
        cv2.rectangle(annotated_img, (x1, y1), (x2, y2), (0, 255, 255), thickness)
        # 绘制追踪ID
        if track_ids is not None and i < len(track_ids):
            track_id = int(track_ids[i])
            cv2.putText(annotated_img, f"ID:{track_id}", (x1, y1-10), 
                       cv2.FONT_HERSHEY_SIMPLEX, 0.6, (0, 255, 0), 2)
    return annotated_img

def calculate_scaled_resolution(original_width, original_height, target_size=1280):
    """计算按比例缩放后的分辨率，确保长边为目标尺寸且宽高为偶数。"""
    if original_width == 0 or original_height == 0:
        return target_size, int(target_size / (16/9))

    if original_width <= target_size and original_height <= target_size:
        new_width = original_width if original_width % 2 == 0 else original_width - 1
        new_height = original_height if original_height % 2 == 0 else original_height - 1
        return new_width, new_height

    if original_width > original_height:
        aspect_ratio = original_height / original_width
        new_width = target_size
        new_height = int(new_width * aspect_ratio)
    else:
        aspect_ratio = original_width / original_height
        new_height = target_size
        new_width = int(new_height * aspect_ratio)

    new_width = new_width if new_width % 2 == 0 else new_width - 1
    new_height = new_height if new_height % 2 == 0 else new_height - 1
    
    return new_width, new_height

def send_report_to_api(report_data):
    """
    将报告数据通过POST请求发送到API端点。
    在后台线程中执行，以避免阻塞主处理流程。
    """
    def task():
        if API_ENDPOINT == "https://your.api/endpoint/here":
            # 如果API未配置，仅打印提示，不发送请求
            return
        
        try:
            headers = {'Content-Type': 'application/json'}
            response = requests.post(API_ENDPOINT, headers=headers, json=report_data, timeout=10)
            if 200 <= response.status_code < 300:
                print(f"✅ 报告成功发送到 API, 状态码: {response.status_code}")
            else:
                print(f"⚠️ API 上报警告: 状态码 {response.status_code}, 响应: {response.text[:200]}")
        except requests.exceptions.RequestException as e:
            print(f"❌ API 上报错误: {e}")

    threading.Thread(target=task, daemon=True).start()

# --- 个体猪只追踪管理器 ---
class PigTracker:
    """管理每头猪的追踪状态和计数器"""
    def __init__(self, min_consecutive_frames):
        self.min_consecutive_frames = min_consecutive_frames
        self.pig_trackers = {}  # {track_id: consecutive_frames_count}
        self.valid_pigs = set()  # 有效猪的ID集合
        self.last_seen_frame = {}  # {track_id: frame_number}
        self.frame_count = 0
        
    def update(self, track_ids):
        """更新追踪状态"""
        self.frame_count += 1
        current_track_ids = set(track_ids) if track_ids else set()
        
        # 更新当前帧中出现的猪
        for track_id in current_track_ids:
            if track_id not in self.pig_trackers:
                self.pig_trackers[track_id] = 0
            self.pig_trackers[track_id] += 1
            self.last_seen_frame[track_id] = self.frame_count
            
            # 检查是否达到有效猪的标准
            if self.pig_trackers[track_id] >= self.min_consecutive_frames:
                self.valid_pigs.add(track_id)
        
        # 处理未出现的猪（重置计数器）
        pigs_to_remove = []
        for track_id in self.pig_trackers:
            if track_id not in current_track_ids:
                # 如果连续5帧未出现，则重置计数器
                if self.frame_count - self.last_seen_frame.get(track_id, 0) > 5:
                    self.pig_trackers[track_id] = 0
                    if track_id in self.valid_pigs:
                        self.valid_pigs.remove(track_id)
                    pigs_to_remove.append(track_id)
        
        # 清理长时间未出现的追踪记录
        for track_id in pigs_to_remove:
            if self.frame_count - self.last_seen_frame.get(track_id, 0) > 100:  # 100帧后完全清理
                del self.pig_trackers[track_id]
                if track_id in self.last_seen_frame:
                    del self.last_seen_frame[track_id]
        
        return len(self.valid_pigs)
    
    def get_tracking_info(self):
        """获取追踪信息用于调试"""
        return {
            'total_tracked': len(self.pig_trackers),
            'valid_pigs': len(self.valid_pigs),
            'trackers': dict(self.pig_trackers),
            'valid_ids': list(self.valid_pigs)
        }

# --- 核心流媒体服务类 ---
class PigDetectionStreamer:
    """管理HLS流媒体服务的核心类。"""
    def __init__(self, stream_port=6006):
        self.stream_port = stream_port
        self.hls_dir = "hls_output"
        os.makedirs(self.hls_dir, exist_ok=True)
        self.app = Flask(__name__)
        CORS(self.app)
        self.setup_routes()
        
    def setup_routes(self):
        player_html = """
        <!DOCTYPE html><html><head><title>Live Pig Detection Stream</title><meta charset="UTF-8"><script src="https://cdn.jsdelivr.net/npm/hls.js@latest"></script><style>body, html { margin: 0; padding: 0; height: 100%; background-color: #000; overflow: hidden; } video { width: 100%; height: 100%; object-fit: contain; }</style></head><body><video id="video" controls autoplay muted></video><script>const video = document.getElementById('video'); const hls_url = '/live.m3u8'; if (Hls.isSupported()) { const hls = new Hls(); hls.loadSource(hls_url); hls.attachMedia(video); } else if (video.canPlayType('application/vnd.apple.mpegurl')) { video.src = hls_url; }</script></body></html>
        """
        @self.app.route('/')
        def index(): return render_template_string(player_html)
        @self.app.route('/live.m3u8')
        def serve_m3u8(): return send_file(os.path.join(self.hls_dir, "live.m3u8"), mimetype='application/vnd.apple.mpegurl')
        @self.app.route('/live<int:segment_num>.ts')
        def serve_segment(segment_num): return send_file(os.path.join(self.hls_dir, f"live{segment_num:03d}.ts"), mimetype='video/mp2t')

def start_streaming(model, source, conf_threshold=0.25, iou_threshold=0.45, target_size=1280,
                   max_det=300, stream_port=6006, imgsz=640, public_url="", tracker_config="bytetrack.yaml"):
    """
    启动从视频源进行检测并推送到HLS的流媒体服务。
    """
    streamer = PigDetectionStreamer(stream_port)
    
    # 快照文件夹和文件名优化
    snapshots_dir = "snapshots"
    os.makedirs(snapshots_dir, exist_ok=True)
    snapshot_path = os.path.join(snapshots_dir, "latest_snapshot.jpg")
    print(f"ℹ️ 快照图片将保存并覆盖于: '{snapshot_path}'")
    
    def run_hls_server():
        try:
            from waitress import serve
            serve(streamer.app, host='0.0.0.0', port=streamer.stream_port)
        except ImportError:
            streamer.app.run(host='0.0.0.0', port=streamer.stream_port)
        
    hls_thread = threading.Thread(target=run_hls_server, daemon=True)
    hls_thread.start()
    
    # 2. 正确处理和打印 URL
    hls_stream_url = ""
    player_page_url = ""
    print("\n" + "="*60)
    print("🚀 HLS 流媒体服务已启动 🚀")
    if public_url:
        # 移除末尾的斜杠，以保证拼接URL的正确性
        base_url = public_url.rstrip('/')
        hls_stream_url = f"{base_url}/live.m3u8"
        player_page_url = f"{base_url}/"
        print(f"✅ 在VLC或支持HLS的播放器中打开此URL: {hls_stream_url}")
        print(f"🌐 或在浏览器中访问播放页面: {player_page_url}")
    else:
        print("⚠️ 警告: 未通过 --public-url 提供公网URL。上报的URL将为空。")
        print(f"   请使用服务商提供的公网地址访问，内部服务运行在 0.0.0.0:{stream_port}")
    print("="*60 + "\n")
    
    cap = cv2.VideoCapture(source)
    if not cap.isOpened():
        print(f"❌ 错误: 无法打开视频源 {source}"); return

    original_fps = cap.get(cv2.CAP_PROP_FPS) or 25
    original_width = int(cap.get(cv2.CAP_PROP_FRAME_WIDTH))
    original_height = int(cap.get(cv2.CAP_PROP_FRAME_HEIGHT))
    print(f"ℹ️ 源信息: {original_width}x{original_height} @ {original_fps:.2f} FPS")
    scaled_width, scaled_height = calculate_scaled_resolution(original_width, original_height, target_size=target_size)
    print(f"✅ 输出流将缩放至: {scaled_width}x{scaled_height}")

    ffmpeg_cmd = [
        'ffmpeg', '-loglevel', 'error', '-y', '-f', 'rawvideo', '-vcodec', 'rawvideo', '-pix_fmt', 'bgr24',
        '-s', f'{scaled_width}x{scaled_height}',
        '-r', str(original_fps), '-i', '-', '-c:v', 'libx264', '-preset', 'ultrafast',
        '-tune', 'zerolatency', '-g', str(int(original_fps * 2)), '-sc_threshold', '0',
        '-f', 'hls', '-hls_time', '2', '-hls_list_size', '5',
        '-hls_flags', 'delete_segments+program_date_time',
        '-hls_segment_filename', os.path.join(streamer.hls_dir, 'live%03d.ts'),
        os.path.join(streamer.hls_dir, 'live.m3u8'),
    ]
    ffmpeg_process = subprocess.Popen(ffmpeg_cmd, stdin=subprocess.PIPE)
    
    # 功能模块变量初始化
    min_consecutive_frames = 1*int(original_fps)
    pig_tracker = PigTracker(min_consecutive_frames)
    reporting_interval = 15
    last_report_time = time.time()
    interval_max_pigs = 0
    interval_best_frame_time = None
    interval_best_frame = None

    print(f"🔍 追踪器已初始化，最小连续帧数: {min_consecutive_frames}")
    print(f"📊 使用追踪器: {tracker_config}")

    try:
        while True:
            ret, frame = cap.read()
            if not ret:
                print("⚠️ 视频流中断，尝试重连..."); cap.release(); time.sleep(2)
                cap = cv2.VideoCapture(source)
                if not cap.isOpened(): print("❌ 重连失败，退出。"); break
                continue
            
            resized_frame = cv2.resize(frame, (scaled_width, scaled_height), interpolation=cv2.INTER_AREA)
            
            # 使用追踪功能替代普通检测
            results = model.track(resized_frame, conf=conf_threshold, iou=iou_threshold, 
                                max_det=max_det, imgsz=imgsz, verbose=False, tracker=tracker_config)
            
            # 提取追踪ID
            track_ids = []
            if results[0].boxes is not None and hasattr(results[0].boxes, 'id') and results[0].boxes.id is not None:
                track_ids = results[0].boxes.id.cpu().numpy().astype(int).tolist()
            
            # 更新追踪器状态
            stable_pig_count = pig_tracker.update(track_ids)
            
            # 记录最佳帧
            if stable_pig_count > interval_max_pigs:
                interval_max_pigs = stable_pig_count
                interval_best_frame_time = datetime.now()
                interval_best_frame = resized_frame.copy()
                cv2.imwrite(snapshot_path, interval_best_frame)

            # 绘制带ID的边界框
            annotated_frame = draw_boxes_with_ids(resized_frame, results[0].boxes, track_ids)
            
            try:
                ffmpeg_process.stdin.write(annotated_frame.tobytes())
            except (BrokenPipeError, OSError):
                print("❌ FFmpeg 进程关闭，停止推流。"); break

            current_time = time.time()
            if current_time - last_report_time >= reporting_interval:
                base64_picture = None
                if interval_best_frame is not None:
                    try:
                        _, buffer = cv2.imencode('.jpg', interval_best_frame)
                        base64_picture = base64.b64encode(buffer).decode('utf-8')
                    except Exception as e:
                        print(f"❌ 图片Base64编码失败: {e}")

                report_data = {
                    "pig_amount": interval_max_pigs,
                    "detect_time": interval_best_frame_time.strftime('%Y-%m-%d %H:%M:%S') if interval_best_frame_time else datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
                    "url": hls_stream_url,
                    "picture": base64_picture,
                }

                # 简化的日志打印
                log_data = report_data.copy()
                if log_data['picture']:
                    log_data['picture'] = f"Base64 string (size: {len(log_data['picture'])} bytes)"
                
                print("\n--- 15秒周期报告 (准备上报) ---")
                print(json.dumps(log_data, ensure_ascii=False, indent=4))
                print("--------------------------------\n")

                send_report_to_api(report_data)
                
                last_report_time = current_time
                interval_max_pigs = 0
                interval_best_frame_time = None
                interval_best_frame = None

    except KeyboardInterrupt:
        print("\nℹ️ 收到中断信号，正在停止服务...")
    finally:
        cap.release()
        if ffmpeg_process and ffmpeg_process.stdin:
            try: ffmpeg_process.stdin.close()
            except (BrokenPipeError, OSError): pass
        if ffmpeg_process: ffmpeg_process.wait()
        print("✅ 服务已安全停止。")

def main():
    parser = argparse.ArgumentParser(description='猪检测智能推流脚本 - 个体追踪版')
    parser.add_argument('--model', type=str, default='runs/detect_100_640/pig_detection/weights/best.pt', help='模型路径')
    parser.add_argument('--source', type=str, required=True, help='输入视频流 (例如 "rtmp://..." 或 "0")')
    parser.add_argument('--conf', type=float, default=0.25, help='置信度阈值')
    parser.add_argument('--iou', type=float, default=0.45, help='NMS IOU阈值')
    parser.add_argument('--target_size', type=int, default=1280, help='目标分辨率')
    parser.add_argument('--imgsz', type=int, default=640, help='推理图片大小')
    parser.add_argument('--port', type=int, default=6006, help='HLS流媒体服务端口')
    parser.add_argument('--device', type=str, default='', help='推理设备 (cpu, 0, ...)')
    parser.add_argument('--public-url', type=str, default='https://u8076-b250-ef46accb.bjb1.seetacloud.com:8443', help='AutoDL 提供的公网访问 URL')
    parser.add_argument('--tracker', type=str, default='bytetrack.yaml', help='追踪器配置文件 (bytetrack.yaml 或 botsort.yaml)')
    args = parser.parse_args()

    if not os.path.exists(args.model):
        print(f"❌ 错误: 模型文件 {args.model} 不存在!"); return
        
    if API_ENDPOINT == "https://your.api/endpoint/here":
        print("⚠️ 警告: API端点未配置。周期报告将仅在本地打印，不会发送到远程服务器。")
        print("   请修改脚本中的 `API_ENDPOINT` 变量以启用上报功能。")

    print("🧠 正在加载模型...")
    model = YOLO(args.model)
    if args.device: model.to(args.device)
    
    print(f"🔍 追踪器配置: {args.tracker}")
    
    start_streaming(model, args.source, args.conf, args.iou, target_size=args.target_size,
                   max_det=300, stream_port=args.port, imgsz=args.imgsz, public_url=args.public_url,
                   tracker_config=args.tracker)

if __name__ == "__main__":
    main()