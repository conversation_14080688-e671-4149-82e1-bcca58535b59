#!/usr/bin/env python3
"""
YOLOv8 猪检测智能推流脚本 (v2.1 - 修正版)

功能特性:
- 输入: RTMP, RTSP, 摄像头等视频流
- 输出: 稳定的HLS (m3u8) 直播流
- 分辨率优化: 等比缩放至1280px长边
- 帧率同步与稳定性监控
- 智能统计: 过滤短暂出现的检测目标
- 周期性报告: 每15秒通过API上报JSON数据
- 快照优化: 仅保留最新的单张快照图片
- 数据上报: 图片以Base64格式随JSON一同POST到远程服务器
"""

import os
import argparse
import threading
import subprocess
import time
import socket
import json
import base64
from collections import deque
from datetime import datetime

from ultralytics import YOLO
import cv2
import requests
from flask import Flask, send_file, render_template_string
from flask_cors import CORS

# --- API 上报配置 ---
# !!! 重要: 请将此URL替换为您真实的API接收端点 !!!
API_ENDPOINT = "http://***************:48902/api/AnalysisResult/Push"

# --- 辅助函数 ---

def get_local_ip():
    """获取本机局域网IP地址，以便生成可访问的URL。"""
    s = socket.socket(socket.AF_INET, socket.SOCK_DGRAM)
    try:
        s.connect(('**************', 1))
        IP = s.getsockname()[0]
    except Exception:
        IP = '127.0.0.1'
    finally:
        s.close()
    return IP

def draw_boxes_only(image, boxes, thickness=2):
    """仅在图像上绘制边界框。"""
    if boxes is None:
        return image
    annotated_img = image.copy()
    for box in boxes:
        x1, y1, x2, y2 = box.xyxy[0].cpu().numpy().astype(int)
        cv2.rectangle(annotated_img, (x1, y1), (x2, y2), (0, 255, 255), thickness)
    return annotated_img

def calculate_scaled_resolution(original_width, original_height, target_size=1280):
    """计算按比例缩放后的分辨率，确保长边为目标尺寸且宽高为偶数。"""
    if original_width == 0 or original_height == 0:
        return target_size, int(target_size / (16/9))

    if original_width <= target_size and original_height <= target_size:
        new_width = original_width if original_width % 2 == 0 else original_width - 1
        new_height = original_height if original_height % 2 == 0 else original_height - 1
        return new_width, new_height

    if original_width > original_height:
        aspect_ratio = original_height / original_width
        new_width = target_size
        new_height = int(new_width * aspect_ratio)
    else:
        aspect_ratio = original_width / original_height
        new_height = target_size
        new_width = int(new_height * aspect_ratio)

    new_width = new_width if new_width % 2 == 0 else new_width - 1
    new_height = new_height if new_height % 2 == 0 else new_height - 1
    
    return new_width, new_height

def send_report_to_api(report_data):
    """
    将报告数据通过POST请求发送到API端点。
    在后台线程中执行，以避免阻塞主处理流程。
    """
    def task():
        if API_ENDPOINT == "https://your.api/endpoint/here":
            # 如果API未配置，仅打印提示，不发送请求
            return
        
        try:
            headers = {'Content-Type': 'application/json'}
            response = requests.post(API_ENDPOINT, headers=headers, json=report_data, timeout=10)
            if 200 <= response.status_code < 300:
                print(f"✅ 报告成功发送到 API, 状态码: {response.status_code}")
            else:
                print(f"⚠️ API 上报警告: 状态码 {response.status_code}, 响应: {response.text[:200]}")
        except requests.exceptions.RequestException as e:
            print(f"❌ API 上报错误: {e}")

    threading.Thread(target=task, daemon=True).start()


# --- 核心流媒体服务类 ---
class PigDetectionStreamer:
    """管理HLS流媒体服务的核心类。"""
    def __init__(self, stream_port=6006):
        self.stream_port = stream_port
        self.hls_dir = "hls_output"
        os.makedirs(self.hls_dir, exist_ok=True)
        self.app = Flask(__name__)
        CORS(self.app)
        self.setup_routes()
        
    def setup_routes(self):
        player_html = """
        <!DOCTYPE html><html><head><title>Live Pig Detection Stream</title><meta charset="UTF-8"><script src="https://cdn.jsdelivr.net/npm/hls.js@latest"></script><style>body, html { margin: 0; padding: 0; height: 100%; background-color: #000; overflow: hidden; } video { width: 100%; height: 100%; object-fit: contain; }</style></head><body><video id="video" controls autoplay muted></video><script>const video = document.getElementById('video'); const hls_url = '/live.m3u8'; if (Hls.isSupported()) { const hls = new Hls(); hls.loadSource(hls_url); hls.attachMedia(video); } else if (video.canPlayType('application/vnd.apple.mpegurl')) { video.src = hls_url; }</script></body></html>
        """
        @self.app.route('/')
        def index(): return render_template_string(player_html)
        @self.app.route('/live.m3u8')
        def serve_m3u8(): return send_file(os.path.join(self.hls_dir, "live.m3u8"), mimetype='application/vnd.apple.mpegurl')
        @self.app.route('/live<int:segment_num>.ts')
        def serve_segment(segment_num): return send_file(os.path.join(self.hls_dir, f"live{segment_num:03d}.ts"), mimetype='video/mp2t')

def start_streaming(model, source, conf_threshold=0.25, iou_threshold=0.45, target_size=1280,
                   max_det=300, stream_port=6006, imgsz=640, public_url=""):
    """
    启动从视频源进行检测并推送到HLS的流媒体服务。
    """
    streamer = PigDetectionStreamer(stream_port)
    
    # 快照文件夹和文件名优化
    snapshots_dir = "snapshots"
    os.makedirs(snapshots_dir, exist_ok=True)
    snapshot_path = os.path.join(snapshots_dir, "latest_snapshot.jpg")
    print(f"ℹ️ 快照图片将保存并覆盖于: '{snapshot_path}'")
    
    def run_hls_server():
        try:
            from waitress import serve
            serve(streamer.app, host='0.0.0.0', port=streamer.stream_port)
        except ImportError:
            streamer.app.run(host='0.0.0.0', port=streamer.stream_port)
        
    hls_thread = threading.Thread(target=run_hls_server, daemon=True)
    hls_thread.start()
    
    # 2. 正确处理和打印 URL
    hls_stream_url = ""
    player_page_url = ""
    print("\n" + "="*60)
    print("🚀 HLS 流媒体服务已启动 🚀")
    if public_url:
        # 移除末尾的斜杠，以保证拼接URL的正确性
        base_url = public_url.rstrip('/')
        hls_stream_url = f"{base_url}/live.m3u8"
        player_page_url = f"{base_url}/"
        print(f"✅ 在VLC或支持HLS的播放器中打开此URL: {hls_stream_url}")
        print(f"🌐 或在浏览器中访问播放页面: {player_page_url}")
    else:
        print("⚠️ 警告: 未通过 --public-url 提供公网URL。上报的URL将为空。")
        print(f"   请使用服务商提供的公网地址访问，内部服务运行在 0.0.0.0:{stream_port}")
    print("="*60 + "\n")
    
    cap = cv2.VideoCapture(source)
    if not cap.isOpened():
        print(f"❌ 错误: 无法打开视频源 {source}"); return

    original_fps = cap.get(cv2.CAP_PROP_FPS) or 25
    original_width = int(cap.get(cv2.CAP_PROP_FRAME_WIDTH))
    original_height = int(cap.get(cv2.CAP_PROP_FRAME_HEIGHT))
    print(f"ℹ️ 源信息: {original_width}x{original_height} @ {original_fps:.2f} FPS")
    scaled_width, scaled_height = calculate_scaled_resolution(original_width, original_height, target_size=target_size)
    print(f"✅ 输出流将缩放至: {scaled_width}x{scaled_height}")

    ffmpeg_cmd = [
        'ffmpeg', '-loglevel', 'error', '-y', '-f', 'rawvideo', '-vcodec', 'rawvideo', '-pix_fmt', 'bgr24',
        '-s', f'{scaled_width}x{scaled_height}',
        '-r', str(original_fps), '-i', '-', '-c:v', 'libx264', '-preset', 'ultrafast',
        '-tune', 'zerolatency', '-g', str(int(original_fps * 2)), '-sc_threshold', '0',
        '-f', 'hls', '-hls_time', '2', '-hls_list_size', '5',
        '-hls_flags', 'delete_segments+program_date_time',
        '-hls_segment_filename', os.path.join(streamer.hls_dir, 'live%03d.ts'),
        os.path.join(streamer.hls_dir, 'live.m3u8'),
    ]
    ffmpeg_process = subprocess.Popen(ffmpeg_cmd, stdin=subprocess.PIPE)

    # 功能模块变量初始化
    min_consecutive_frames = 1 * int(original_fps)  # 稳定检测所需的连续帧数 (例如1秒)
    # 新增：用于跟踪每头猪的连续帧数计数器 {track_id: frame_count}
    pig_track_counters = {}
    
    reporting_interval = 15  # 报告周期（秒）
    last_report_time = time.time()
    interval_max_pigs = 0
    interval_best_frame_time = None
    interval_best_frame = None

    try:
        while True:
            ret, frame = cap.read()
            if not ret:
                print("⚠️ 视频流中断，尝试重连..."); cap.release(); time.sleep(2)
                cap = cv2.VideoCapture(source)
                if not cap.isOpened(): print("❌ 重连失败，退出。"); break
                continue
            
            resized_frame = cv2.resize(frame, (scaled_width, scaled_height), interpolation=cv2.INTER_AREA)
            
            # 修改：使用 model.track() 来获取带ID的检测结果
            results = model.track(resized_frame, persist=True, conf=conf_threshold, iou=iou_threshold, max_det=max_det, imgsz=imgsz, verbose=False)
            
            stable_pig_count = 0
            current_track_ids = set()

            # 检查是否有跟踪结果并且结果中包含ID
            if results[0].boxes is not None and results[0].boxes.id is not None:
                # 1. 获取当前帧所有被跟踪到的猪的ID
                current_track_ids = set(results[0].boxes.id.cpu().numpy().astype(int))

                # 2. 更新或初始化这些ID的计数器
                for track_id in current_track_ids:
                    # 如果ID已在跟踪，计数值+1；如果是新ID，则从0+1=1开始计数
                    pig_track_counters[track_id] = pig_track_counters.get(track_id, 0) + 1
            
            # 3. 清理已消失的猪的计数器
            # 找出那些之前在跟踪但当前帧已消失的ID
            disappeared_ids = set(pig_track_counters.keys()) - current_track_ids
            for track_id in disappeared_ids:
                del pig_track_counters[track_id] # 从字典中移除，实现计数清零

            # 4. 计算当前帧的“稳定”猪只数量
            # 遍历所有仍在跟踪的猪，如果其连续出现帧数达标，则计为稳定猪
            if pig_track_counters:
                for track_id in pig_track_counters:
                    if pig_track_counters[track_id] >= min_consecutive_frames:
                        stable_pig_count += 1

            if stable_pig_count > interval_max_pigs:
                interval_max_pigs = stable_pig_count
                interval_best_frame_time = datetime.now()
                # 仅在找到更优结果时才复制和保存帧，优化性能
                interval_best_frame = resized_frame.copy() 
                # 我们只绘制检测框，不绘制跟踪ID和置信度，以保持画面简洁
                annotated_best_frame = draw_boxes_only(interval_best_frame, results[0].boxes)
                cv2.imwrite(snapshot_path, annotated_best_frame)

            annotated_frame = draw_boxes_only(resized_frame, results[0].boxes)
            try:
                ffmpeg_process.stdin.write(annotated_frame.tobytes())
            except (BrokenPipeError, OSError):
                print("❌ FFmpeg 进程关闭，停止推流。"); break

            current_time = time.time()
            if current_time - last_report_time >= reporting_interval:
                base64_picture = None
                # 注意：这里我们上报的是快照文件，而不是内存中的frame
                if os.path.exists(snapshot_path):
                    try:
                        with open(snapshot_path, "rb") as image_file:
                            base64_picture = base64.b64encode(image_file.read()).decode('utf-8')
                    except Exception as e:
                        print(f"❌ 图片读取或Base64编码失败: {e}")

                report_data = {
                    "pig_amount": interval_max_pigs,
                    "detect_time": interval_best_frame_time.strftime('%Y-%m-%d %H:%M:%S') if interval_best_frame_time else datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
                    "url": hls_stream_url,
                    "picture": base64_picture
                }
                
                log_data = report_data.copy()
                if log_data.get('picture'):
                    log_data['picture'] = f"Base64 string (size: {len(log_data['picture'])} bytes)"
                
                print("\n--- 15秒周期报告 (准备上报) ---")
                print(f"有效猪只数量: {report_data['pig_amount']}")
                print(f"最佳检测时间: {report_data['detect_time']}")
                # print(json.dumps(log_data, ensure_ascii=False, indent=4))
                print("--------------------------------\n")

                send_report_to_api(report_data)
                
                # 重置周期统计变量
                last_report_time = current_time
                interval_max_pigs = 0
                interval_best_frame_time = None
                interval_best_frame = None
                # 清理快照，避免旧图片被误用
                if os.path.exists(snapshot_path):
                    try:
                        os.remove(snapshot_path)
                    except OSError as e:
                        print(f"⚠️ 清理快照失败: {e}")

    except KeyboardInterrupt:
        print("\nℹ️ 收到中断信号，正在停止服务...")
    finally:
        cap.release()
        if ffmpeg_process and ffmpeg_process.stdin:
            try: ffmpeg_process.stdin.close()
            except (BrokenPipeError, OSError): pass
        if ffmpeg_process: ffmpeg_process.wait()
        print("✅ 服务已安全停止。")


def main():
    parser = argparse.ArgumentParser(description='猪检测智能推流脚本')
    parser.add_argument('--model', type=str, default='runs/detect_100_640/pig_detection/weights/best.pt', help='模型路径')
    parser.add_argument('--source', type=str, required=True, help='输入视频流 (例如 "rtmp://..." 或 "0")')
    parser.add_argument('--conf', type=float, default=0.25, help='置信度阈值')
    parser.add_argument('--iou', type=float, default=0.45, help='NMS IOU阈值')
    parser.add_argument('--target_size', type=int, default=1280, help='目标分辨率')
    parser.add_argument('--imgsz', type=int, default=640, help='推理图片大小')
    parser.add_argument('--port', type=int, default=6006, help='HLS流媒体服务端口')
    parser.add_argument('--device', type=str, default='', help='推理设备 (cpu, 0, ...)')
    parser.add_argument('--public-url', type=str, default='https://u8076-b250-ef46accb.bjb1.seetacloud.com:8443', help='AutoDL 提供的公网访问 URL')
    args = parser.parse_args()

    if not os.path.exists(args.model):
        print(f"❌ 错误: 模型文件 {args.model} 不存在!"); return
        
    if API_ENDPOINT == "https://your.api/endpoint/here":
        print("⚠️ 警告: API端点未配置。周期报告将仅在本地打印，不会发送到远程服务器。")
        print("   请修改脚本中的 `API_ENDPOINT` 变量以启用上报功能。")

    print("🧠 正在加载模型...")
    model = YOLO(args.model)
    if args.device: model.to(args.device)
    
    start_streaming(model, args.source, args.conf, args.iou, target_size=args.target_size,
                   max_det=300, stream_port=args.port, imgsz=args.imgsz, public_url=args.public_url)

if __name__ == "__main__":
    main()
